services:
  apache:
    build:
      context: .
      dockerfile: docker/php/Dockerfile
      args:
        USER_ID: 1000
        GROUP_ID: 1000
    container_name: symfony_apache
    ports:
      - "8000:80"
    volumes:
      - ./symfony-app:/var/www/html
      - ./docker/apache/vhost.conf:/etc/apache2/sites-available/000-default.conf
    depends_on:
      - mysql
  mysql:
    image: mysql:5.7
    container_name: symfony_mysql
    restart: always
    environment:
      MYSQL_DATABASE: symfony
      MYSQL_USER: symfony
      MYSQL_PASSWORD: secret
      MYSQL_ROOT_PASSWORD: root
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: symfony_phpmyadmin
    restart: always
    ports:
      - "8080:80"
    environment:
      PMA_HOST: mysql
      PMA_PORT: 3306
volumes:
  mysql_data: