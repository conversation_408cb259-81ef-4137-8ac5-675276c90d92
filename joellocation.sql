-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Hôte : 127.0.0.1:3306
-- <PERSON><PERSON><PERSON><PERSON> le : jeu. 24 juil. 2025 à 03:35
-- Version du serveur : 8.3.0
-- Version de PHP : 7.4.33

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Base de données : `joellocation`
--

-- --------------------------------------------------------

--
-- Structure de la table `agence`
--

DROP TABLE IF EXISTS `agence`;
CREATE TABLE IF NOT EXISTS `agence` (
  `id` int NOT NULL AUTO_INCREMENT,
  `nom` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `presentation` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `mail` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `telephone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `annulation_reservation`
--

DROP TABLE IF EXISTS `annulation_reservation`;
CREATE TABLE IF NOT EXISTS `annulation_reservation` (
  `id` int NOT NULL AUTO_INCREMENT,
  `reservation_id` int NOT NULL,
  `created_at` datetime NOT NULL,
  `motif` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UNIQ_4418C7BBB83297E7` (`reservation_id`)
) ENGINE=InnoDB AUTO_INCREMENT=25 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `annulation_reservation`
--

INSERT INTO `annulation_reservation` (`id`, `reservation_id`, `created_at`, `motif`, `type`) VALUES
(1, 59, '2022-03-29 09:22:36', 'Test', 'sans_avoir'),
(2, 71, '2022-07-09 19:45:06', 'RESEVATION-IMPOSIBLE', 'sans_avoir'),
(11, 103, '2022-08-25 01:34:21', 'EREUR VOITURE', 'avec_avoir'),
(12, 105, '2022-08-25 01:52:45', 'EREUR VOITURE', 'avec_avoir'),
(13, 80, '2022-09-02 19:05:28', 'Changement véhicule', 'sans_avoir'),
(14, 136, '2022-11-05 20:30:21', 'kk', 'avec_avoir'),
(15, 157, '2022-12-12 02:02:50', 'plus d\'utilite', 'avec_avoir'),
(16, 168, '2023-01-04 05:33:43', 'jeunne permis', 'avec_avoir'),
(17, 154, '2023-02-12 15:54:23', 'aa', 'sans_avoir'),
(18, 210, '2023-07-22 07:08:17', 'VOITURE NON DISPONIBLE (EN REPARATION)', 'sans_avoir'),
(19, 264, '2024-01-08 16:26:05', 'ERREUR', 'sans_avoir'),
(20, 267, '2024-01-16 02:51:12', 'panne voiture', 'sans_avoir'),
(21, 333, '2024-06-08 00:33:01', 'JKL', 'sans_avoir'),
(22, 299, '2024-07-04 20:27:08', 'changement loueur coco', 'avec_avoir'),
(23, 262, '2024-08-09 14:42:56', 'sans motif', 'sans_avoir'),
(24, 369, '2024-09-24 02:09:30', 'PROBLEME DE CARTE', 'sans_avoir');

-- --------------------------------------------------------

--
-- Structure de la table `appel_paiement`
--

DROP TABLE IF EXISTS `appel_paiement`;
CREATE TABLE IF NOT EXISTS `appel_paiement` (
  `id` int NOT NULL AUTO_INCREMENT,
  `reservation_id` int DEFAULT NULL,
  `montant` double NOT NULL,
  `date_demande` datetime DEFAULT NULL,
  `payed` tinyint(1) NOT NULL,
  `date_paiement` datetime DEFAULT NULL,
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `sent_dates` longtext COLLATE utf8mb4_unicode_ci COMMENT '(DC2Type:json)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `UNIQ_CD9A1FBCB83297E7` (`reservation_id`)
) ENGINE=InnoDB AUTO_INCREMENT=321 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `appel_paiement`
--

INSERT INTO `appel_paiement` (`id`, `reservation_id`, `montant`, `date_demande`, `payed`, `date_paiement`, `type`, `sent_dates`) VALUES
(11, 23, 130, NULL, 0, NULL, NULL, NULL),
(12, 20, 130, NULL, 0, NULL, NULL, '[\"2025-07-21 16:28:35\"]'),
(13, 21, 620, NULL, 0, NULL, NULL, '[\"2025-07-21 16:27:55\",\"2025-07-21 16:30:08\"]'),
(14, 27, 250, NULL, 0, NULL, NULL, NULL),
(15, 26, 675, NULL, 0, NULL, NULL, NULL),
(16, 25, 837, '2022-02-08 05:23:13', 0, NULL, NULL, NULL),
(17, 24, 705, NULL, 0, NULL, NULL, NULL),
(18, 29, 263.7, NULL, 0, NULL, NULL, NULL),
(19, 62, 60, NULL, 0, NULL, NULL, NULL),
(20, 61, 137, NULL, 0, NULL, NULL, NULL),
(21, 60, 435, NULL, 0, NULL, NULL, NULL),
(22, 59, 60, NULL, 0, NULL, NULL, NULL),
(23, 58, 275, NULL, 0, NULL, NULL, NULL),
(24, 56, 435, NULL, 0, NULL, NULL, NULL),
(25, 55, 225, NULL, 0, NULL, NULL, NULL),
(26, 54, 1170, NULL, 0, NULL, NULL, NULL),
(27, 53, 134, NULL, 0, NULL, NULL, NULL),
(28, 50, 686, NULL, 0, NULL, NULL, NULL),
(29, 49, 300, NULL, 0, NULL, NULL, NULL),
(30, 48, 4398.55, NULL, 0, NULL, NULL, NULL),
(31, 47, 175, NULL, 0, NULL, NULL, NULL),
(32, 46, 375, NULL, 0, NULL, NULL, NULL),
(33, 44, 300, NULL, 0, NULL, NULL, NULL),
(34, 43, 745, NULL, 0, NULL, NULL, NULL),
(35, 42, 450, NULL, 0, NULL, NULL, NULL),
(36, 41, 700, NULL, 0, NULL, NULL, NULL),
(37, 40, 375, NULL, 0, NULL, NULL, NULL),
(38, 39, 311.21, NULL, 0, NULL, NULL, NULL),
(39, 38, 188, NULL, 0, NULL, NULL, NULL),
(40, 37, 375, NULL, 0, NULL, NULL, NULL),
(41, 36, 120, NULL, 0, NULL, NULL, NULL),
(42, 33, 585, NULL, 0, NULL, NULL, NULL),
(43, 32, 182.5, NULL, 0, NULL, NULL, NULL),
(44, 31, 167, NULL, 0, NULL, NULL, NULL),
(45, 63, 4, NULL, 0, NULL, NULL, NULL),
(46, 74, 690, NULL, 0, NULL, NULL, NULL),
(47, 73, 623.15, NULL, 0, NULL, NULL, NULL),
(48, 72, 422, NULL, 0, NULL, NULL, NULL),
(49, 71, 350, NULL, 0, NULL, NULL, NULL),
(50, 70, 225, NULL, 0, NULL, NULL, NULL),
(51, 68, 250, NULL, 0, NULL, NULL, NULL),
(52, 67, 450, NULL, 0, NULL, NULL, NULL),
(53, 66, 350, NULL, 0, NULL, NULL, NULL),
(54, 65, 300, NULL, 0, NULL, NULL, NULL),
(55, 64, 700, NULL, 0, NULL, NULL, NULL),
(56, 199, 499, NULL, 0, NULL, NULL, NULL),
(57, 198, 380, NULL, 0, NULL, NULL, NULL),
(58, 197, 180, NULL, 0, NULL, NULL, NULL),
(59, 195, 224, NULL, 0, NULL, NULL, NULL),
(60, 194, 750, NULL, 0, NULL, NULL, NULL),
(61, 193, 604, NULL, 0, NULL, NULL, NULL),
(62, 192, 112.5, NULL, 0, NULL, NULL, NULL),
(63, 191, 224, NULL, 0, NULL, NULL, NULL),
(64, 190, 275, NULL, 0, NULL, NULL, NULL),
(65, 189, 435, NULL, 0, NULL, NULL, NULL),
(66, 188, 464, NULL, 0, NULL, NULL, NULL),
(67, 187, 986, NULL, 0, NULL, NULL, NULL),
(68, 186, 275, NULL, 0, NULL, NULL, NULL),
(69, 184, 135, NULL, 0, NULL, NULL, NULL),
(70, 183, 779, NULL, 0, NULL, NULL, NULL),
(71, 182, 499, NULL, 0, NULL, NULL, NULL),
(72, 181, 425, NULL, 0, NULL, NULL, NULL),
(73, 180, 585, NULL, 0, NULL, NULL, NULL),
(74, 179, 425, NULL, 0, NULL, NULL, NULL),
(75, 177, 519, NULL, 0, NULL, NULL, NULL),
(76, 176, 287, NULL, 0, NULL, NULL, NULL),
(77, 175, 449, NULL, 0, NULL, NULL, NULL),
(78, 174, 550, NULL, 0, NULL, NULL, NULL),
(79, 173, 470, NULL, 0, NULL, NULL, NULL),
(80, 172, 104, NULL, 0, NULL, NULL, NULL),
(81, 171, 300, NULL, 0, NULL, NULL, NULL),
(82, 170, 462, NULL, 0, NULL, NULL, NULL),
(83, 169, 292, NULL, 0, NULL, NULL, NULL),
(84, 167, 759.48, NULL, 0, NULL, NULL, NULL),
(85, 166, 240, NULL, 0, NULL, NULL, NULL),
(86, 165, 194.5, NULL, 0, NULL, NULL, NULL),
(87, 164, 220, NULL, 0, NULL, NULL, NULL),
(88, 163, 229, NULL, 0, NULL, NULL, NULL),
(89, 162, 111, NULL, 0, NULL, NULL, NULL),
(90, 161, 244.5, NULL, 0, NULL, NULL, NULL),
(91, 160, 344, NULL, 0, NULL, NULL, NULL),
(92, 159, 400, NULL, 0, NULL, NULL, NULL),
(93, 158, 439, NULL, 0, NULL, NULL, NULL),
(94, 156, 244.5, NULL, 0, NULL, NULL, NULL),
(95, 155, 604, NULL, 0, NULL, NULL, NULL),
(96, 153, 525, NULL, 0, NULL, NULL, NULL),
(97, 152, 1047, NULL, 0, NULL, NULL, NULL),
(98, 151, 256, NULL, 0, NULL, NULL, NULL),
(99, 150, 180, NULL, 0, NULL, NULL, NULL),
(100, 149, 759.48, NULL, 0, NULL, NULL, NULL),
(101, 148, 319, NULL, 0, NULL, NULL, NULL),
(102, 147, 225, NULL, 0, NULL, NULL, NULL),
(103, 146, 469, NULL, 0, NULL, NULL, NULL),
(104, 145, 200, NULL, 0, NULL, NULL, NULL),
(105, 144, 1170, NULL, 0, NULL, NULL, NULL),
(106, 142, 750, NULL, 0, NULL, NULL, NULL),
(107, 141, 638, NULL, 0, NULL, NULL, NULL),
(108, 140, 319, NULL, 0, NULL, NULL, NULL),
(109, 139, 389, NULL, 0, NULL, NULL, NULL),
(110, 138, 930, NULL, 0, NULL, NULL, NULL),
(111, 137, 1500, NULL, 0, NULL, NULL, NULL),
(112, 135, 319, NULL, 0, NULL, NULL, NULL),
(113, 134, 480, NULL, 0, NULL, NULL, NULL),
(114, 133, 669, NULL, 0, NULL, NULL, NULL),
(115, 132, 167.2, NULL, 0, NULL, NULL, NULL),
(116, 130, 753, NULL, 0, NULL, NULL, NULL),
(117, 129, 450, NULL, 0, NULL, NULL, NULL),
(118, 128, 1220.34, NULL, 0, NULL, NULL, NULL),
(119, 126, 350, NULL, 0, NULL, NULL, NULL),
(120, 125, 669, NULL, 0, NULL, NULL, NULL),
(121, 124, 167.2, NULL, 0, NULL, NULL, NULL),
(122, 123, 700, NULL, 0, NULL, NULL, NULL),
(123, 121, 75, NULL, 0, NULL, NULL, NULL),
(124, 120, 80, NULL, 0, NULL, NULL, NULL),
(125, 119, 420, NULL, 0, NULL, NULL, NULL),
(126, 118, 30, NULL, 0, NULL, NULL, NULL),
(127, 117, 669, NULL, 0, NULL, NULL, NULL),
(128, 115, 800, NULL, 0, NULL, NULL, NULL),
(129, 114, 40, NULL, 0, NULL, NULL, NULL),
(130, 113, 300, NULL, 0, NULL, NULL, NULL),
(131, 112, 120, NULL, 0, NULL, NULL, NULL),
(132, 111, 334, NULL, 0, NULL, NULL, NULL),
(133, 110, 100, NULL, 0, NULL, NULL, NULL),
(134, 108, 334, NULL, 0, NULL, NULL, NULL),
(135, 107, 930, NULL, 0, NULL, NULL, NULL),
(136, 106, 282, NULL, 0, NULL, NULL, NULL),
(137, 104, 152, NULL, 0, NULL, NULL, NULL),
(138, 102, 750, NULL, 0, NULL, NULL, NULL),
(139, 101, 375, NULL, 0, NULL, NULL, NULL),
(140, 100, 100, NULL, 0, NULL, NULL, NULL),
(141, 99, 750, NULL, 0, NULL, NULL, NULL),
(142, 98, 464.15, NULL, 0, NULL, NULL, NULL),
(143, 97, 342.5, NULL, 0, NULL, NULL, NULL),
(144, 96, 450, NULL, 0, NULL, NULL, NULL),
(145, 95, 238, NULL, 0, NULL, NULL, NULL),
(146, 94, 1130, NULL, 0, NULL, NULL, NULL),
(147, 93, 300, NULL, 0, NULL, NULL, NULL),
(148, 88, 300, NULL, 0, NULL, NULL, NULL),
(149, 87, 150, NULL, 0, NULL, NULL, NULL),
(150, 86, 500, NULL, 0, NULL, NULL, NULL),
(151, 84, 660.12, NULL, 0, NULL, NULL, NULL),
(152, 83, 1008, NULL, 0, NULL, NULL, NULL),
(153, 82, 300, NULL, 0, NULL, NULL, NULL),
(154, 79, 119.35, NULL, 0, NULL, NULL, NULL),
(155, 78, 180, NULL, 0, NULL, NULL, NULL),
(156, 77, 200.63, NULL, 0, NULL, NULL, NULL),
(157, 76, 292.95, NULL, 0, NULL, NULL, NULL),
(158, 75, 225, NULL, 0, NULL, NULL, NULL),
(159, 313, 250, NULL, 0, NULL, NULL, NULL),
(160, 311, 551, NULL, 0, NULL, NULL, NULL),
(161, 310, 551, NULL, 0, NULL, NULL, NULL),
(162, 309, 750, NULL, 0, NULL, NULL, NULL),
(163, 308, 70, NULL, 0, NULL, NULL, NULL),
(164, 307, 188, NULL, 0, NULL, NULL, NULL),
(165, 306, 75, NULL, 0, NULL, NULL, NULL),
(166, 305, 350, NULL, 0, NULL, NULL, NULL),
(167, 302, 50, NULL, 0, NULL, NULL, NULL),
(168, 301, 132, NULL, 0, NULL, NULL, NULL),
(169, 300, 110, NULL, 0, NULL, NULL, NULL),
(170, 299, 750, NULL, 0, NULL, NULL, NULL),
(171, 298, 900, NULL, 0, NULL, NULL, NULL),
(172, 297, 275, NULL, 0, NULL, NULL, NULL),
(173, 296, 1170, NULL, 0, NULL, NULL, NULL),
(174, 294, 196, NULL, 0, NULL, NULL, NULL),
(175, 292, 83.7, NULL, 0, NULL, NULL, NULL),
(176, 290, 90, NULL, 0, NULL, NULL, NULL),
(177, 289, 196, NULL, 0, NULL, NULL, NULL),
(178, 288, 450, NULL, 0, NULL, NULL, NULL),
(179, 286, 188, NULL, 0, NULL, NULL, NULL),
(180, 285, 1087, NULL, 0, NULL, NULL, NULL),
(181, 284, 450, NULL, 0, NULL, NULL, NULL),
(182, 282, 425, NULL, 0, NULL, NULL, NULL),
(183, 281, 1220, NULL, 0, NULL, NULL, NULL),
(184, 278, 207.3, NULL, 0, NULL, NULL, NULL),
(185, 277, 252, NULL, 0, NULL, NULL, NULL),
(186, 276, 292, NULL, 0, NULL, NULL, NULL),
(187, 275, 60, NULL, 0, NULL, NULL, NULL),
(188, 274, 270, NULL, 0, NULL, NULL, NULL),
(189, 272, 578, NULL, 0, NULL, NULL, NULL),
(190, 271, 425, NULL, 0, NULL, NULL, NULL),
(191, 270, 930, NULL, 0, NULL, NULL, NULL),
(192, 269, 585, NULL, 0, NULL, NULL, NULL),
(193, 268, 469, NULL, 0, NULL, NULL, NULL),
(194, 266, 220, NULL, 0, NULL, NULL, NULL),
(195, 263, 50, NULL, 0, NULL, NULL, NULL),
(196, 262, 258.99, NULL, 0, NULL, NULL, NULL),
(197, 261, 369.99, NULL, 0, NULL, NULL, NULL),
(198, 260, 50, NULL, 0, NULL, NULL, NULL),
(199, 258, 50, NULL, 0, NULL, NULL, NULL),
(200, 257, 559, NULL, 0, NULL, NULL, NULL),
(201, 256, 437, NULL, 0, NULL, NULL, NULL),
(202, 255, 585, NULL, 0, NULL, NULL, NULL),
(203, 254, 276, NULL, 0, NULL, NULL, NULL),
(204, 253, 244.5, NULL, 0, NULL, NULL, NULL),
(205, 252, 85, NULL, 0, NULL, NULL, NULL),
(206, 251, 720, NULL, 0, NULL, NULL, NULL),
(207, 250, 778, NULL, 0, NULL, NULL, NULL),
(208, 248, 78, NULL, 0, NULL, NULL, NULL),
(209, 247, 525, NULL, 0, NULL, NULL, NULL),
(210, 246, 810, NULL, 0, NULL, NULL, NULL),
(211, 245, 439, NULL, 0, NULL, NULL, NULL),
(212, 242, 233, NULL, 0, NULL, NULL, NULL),
(213, 241, 525, NULL, 0, NULL, NULL, NULL),
(214, 240, 334, NULL, 0, NULL, NULL, NULL),
(215, 239, 495, NULL, 0, NULL, NULL, NULL),
(216, 238, 450, NULL, 0, NULL, NULL, NULL),
(217, 237, 565, NULL, 0, NULL, NULL, NULL),
(218, 236, 167.2, NULL, 0, NULL, NULL, NULL),
(219, 235, 389, NULL, 0, NULL, NULL, NULL),
(220, 234, 384, NULL, 0, NULL, NULL, NULL),
(221, 233, 778, NULL, 0, NULL, NULL, NULL),
(222, 232, 669, NULL, 0, NULL, NULL, NULL),
(223, 231, 719, NULL, 0, NULL, NULL, NULL),
(224, 229, 384, NULL, 0, NULL, NULL, NULL),
(225, 228, 70, NULL, 0, NULL, NULL, NULL),
(226, 227, 202, NULL, 0, NULL, NULL, NULL),
(227, 226, 237.5, NULL, 0, NULL, NULL, NULL),
(228, 225, 1180, NULL, 0, NULL, NULL, NULL),
(229, 224, 635, NULL, 0, NULL, NULL, NULL),
(230, 223, 635, NULL, 0, NULL, NULL, NULL),
(231, 222, 750, NULL, 0, NULL, NULL, NULL),
(232, 221, 334, NULL, 0, NULL, NULL, NULL),
(233, 220, 1101, NULL, 0, NULL, NULL, NULL),
(234, 219, 75, NULL, 0, NULL, NULL, NULL),
(235, 218, 658, NULL, 0, NULL, NULL, NULL),
(236, 217, 425, NULL, 0, NULL, NULL, NULL),
(237, 216, 1130, NULL, 0, NULL, NULL, NULL),
(238, 215, 200, NULL, 0, NULL, NULL, NULL),
(239, 214, 125, NULL, 0, NULL, NULL, NULL),
(240, 213, 1040, NULL, 0, NULL, NULL, NULL),
(241, 212, 750, NULL, 0, NULL, NULL, NULL),
(242, 211, 449, NULL, 0, NULL, NULL, NULL),
(243, 209, 224, NULL, 0, NULL, NULL, NULL),
(244, 208, 450, NULL, 0, NULL, NULL, NULL),
(245, 207, 800, NULL, 0, NULL, NULL, NULL),
(246, 206, 162.5, NULL, 0, NULL, NULL, NULL),
(247, 205, 425, NULL, 0, NULL, NULL, NULL),
(248, 204, 232, NULL, 0, NULL, NULL, NULL),
(249, 203, 132.5, NULL, 0, NULL, NULL, NULL),
(250, 202, 750, NULL, 0, NULL, NULL, NULL),
(251, 201, 375, NULL, 0, NULL, NULL, NULL),
(252, 200, 812, NULL, 0, NULL, NULL, NULL),
(253, 341, 565, NULL, 0, NULL, NULL, NULL),
(254, 340, 500, NULL, 0, NULL, NULL, NULL),
(255, 339, 274, NULL, 0, NULL, NULL, NULL),
(256, 338, 82, NULL, 0, NULL, NULL, NULL),
(257, 337, 82, NULL, 0, NULL, NULL, NULL),
(258, 335, 274, NULL, 0, NULL, NULL, NULL),
(259, 334, 330, NULL, 0, NULL, NULL, NULL),
(260, 332, 492.75, NULL, 0, NULL, NULL, NULL),
(261, 331, 60, NULL, 0, NULL, NULL, NULL),
(262, 330, 50, NULL, 0, NULL, NULL, NULL),
(263, 328, 165, NULL, 0, NULL, NULL, NULL),
(264, 327, 465, NULL, 0, NULL, NULL, NULL),
(265, 325, 165, NULL, 0, NULL, NULL, NULL),
(266, 323, 275, NULL, 0, NULL, NULL, NULL),
(267, 322, 224, NULL, 0, NULL, NULL, NULL),
(268, 321, 750, NULL, 0, NULL, NULL, NULL),
(269, 320, 125, NULL, 0, NULL, NULL, NULL),
(270, 319, 259.99, NULL, 0, NULL, NULL, NULL),
(271, 316, 259.96, NULL, 0, NULL, NULL, NULL),
(272, 315, 519.5, NULL, 0, NULL, NULL, NULL),
(273, 372, 80, NULL, 0, NULL, NULL, NULL),
(274, 371, 425.05, NULL, 0, NULL, NULL, NULL),
(275, 370, 288, NULL, 0, NULL, NULL, NULL),
(276, 368, 450, NULL, 0, NULL, NULL, NULL),
(277, 367, 108, NULL, 0, NULL, NULL, NULL),
(278, 366, 468.05, NULL, 0, NULL, NULL, NULL),
(279, 365, 360, NULL, 0, NULL, NULL, NULL),
(280, 364, 306, NULL, 0, NULL, NULL, NULL),
(281, 363, 1410, NULL, 0, NULL, NULL, NULL),
(282, 362, 187.5, NULL, 0, NULL, NULL, NULL),
(283, 361, 187.5, NULL, 0, NULL, NULL, NULL),
(284, 358, 432, NULL, 0, NULL, NULL, NULL),
(285, 357, 660, NULL, 0, NULL, NULL, NULL),
(286, 356, 450, NULL, 0, NULL, NULL, NULL),
(287, 355, 1198, NULL, 0, NULL, NULL, NULL),
(288, 352, 658, NULL, 0, NULL, NULL, NULL),
(289, 350, 210, NULL, 0, NULL, NULL, NULL),
(290, 349, 700, NULL, 0, NULL, NULL, NULL),
(291, 348, 304, NULL, 0, NULL, NULL, NULL),
(292, 383, 950, NULL, 0, NULL, NULL, NULL),
(293, 382, 530, NULL, 0, NULL, NULL, NULL),
(294, 381, 130, NULL, 0, NULL, NULL, NULL),
(295, 399, 645, NULL, 0, NULL, NULL, NULL),
(296, 398, 760, NULL, 0, NULL, NULL, NULL),
(297, 397, 410, NULL, 0, NULL, NULL, NULL),
(298, 396, 350, NULL, 0, NULL, NULL, NULL),
(299, 395, 350, NULL, 0, NULL, NULL, NULL),
(300, 394, 350, NULL, 0, NULL, NULL, NULL),
(301, 393, 740, NULL, 0, NULL, NULL, NULL),
(302, 392, 220, NULL, 0, NULL, NULL, NULL),
(303, 391, 220, NULL, 0, NULL, NULL, NULL),
(304, 390, 890, NULL, 0, NULL, NULL, NULL),
(305, 389, 650, NULL, 0, NULL, NULL, NULL),
(306, 388, 290, NULL, 0, NULL, NULL, NULL),
(307, 387, 280, NULL, 0, NULL, NULL, NULL),
(308, 386, 280, NULL, 0, NULL, NULL, NULL),
(309, 385, 710, NULL, 0, NULL, NULL, NULL),
(310, 384, 1070, NULL, 0, NULL, NULL, NULL),
(311, 416, 275, NULL, 0, NULL, NULL, NULL),
(312, 415, 554, NULL, 0, NULL, NULL, NULL),
(313, 406, 570, NULL, 0, NULL, NULL, NULL),
(314, 404, 570, NULL, 0, NULL, NULL, NULL),
(315, 403, 570, NULL, 0, NULL, NULL, NULL),
(316, 402, 570, NULL, 0, NULL, NULL, NULL),
(317, 401, 570, NULL, 0, NULL, NULL, NULL),
(318, 400, 570, NULL, 0, NULL, NULL, NULL),
(319, 429, 160, '2025-07-17 20:04:32', 0, NULL, NULL, NULL),
(320, 430, 270, '2025-07-17 20:18:32', 0, NULL, NULL, '[\"2025-07-21 15:50:48\",\"2025-07-21 15:53:33\"]');

-- --------------------------------------------------------

--
-- Structure de la table `avis`
--

DROP TABLE IF EXISTS `avis`;
CREATE TABLE IF NOT EXISTS `avis` (
  `id` int NOT NULL AUTO_INCREMENT,
  `global` int DEFAULT NULL,
  `ponctualite` int DEFAULT NULL,
  `accueil` int DEFAULT NULL,
  `service` int DEFAULT NULL,
  `commentaire` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `date` date NOT NULL,
  `reservation_id` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UNIQ_8F91ABF0B83297E7` (`reservation_id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `conducteur`
--

DROP TABLE IF EXISTS `conducteur`;
CREATE TABLE IF NOT EXISTS `conducteur` (
  `id` int NOT NULL AUTO_INCREMENT,
  `nom` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `date_naissance` date DEFAULT NULL,
  `ville_naissance` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `numero_permis` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `ville_delivrance` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `date_delivrance` date DEFAULT NULL,
  `date_expiration` date DEFAULT NULL,
  `prenom` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `client_id` int DEFAULT NULL,
  `is_principal` tinyint(1) NOT NULL,
  `telephone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_2367714319EB6921` (`client_id`)
) ENGINE=InnoDB AUTO_INCREMENT=46 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `conducteur`
--

INSERT INTO `conducteur` (`id`, `nom`, `date_naissance`, `ville_naissance`, `numero_permis`, `ville_delivrance`, `date_delivrance`, `date_expiration`, `prenom`, `client_id`, `is_principal`, `telephone`) VALUES
(36, 'Ali', '1998-04-03', 'Diego', '11fgr11134o', 'Diego', '2018-06-11', '2018-06-11', 'Dan', NULL, 0, NULL),
(37, 'Popotte', '1996-06-12', 'ROSNY SS BOIS', '16AT129222', 'Préfet 93', '2016-06-10', '2016-06-10', 'Emmanuelle', 173, 0, NULL),
(38, 'S', '2000-07-01', 'DS', '123456', 'DS', '2018-06-13', NULL, 'E', 147, 0, NULL),
(39, 'ACCAJOU', '2022-08-27', 'POINTE A PITRE', 'DFGHJ', 'DFGHJ', '2022-08-24', '2022-08-23', 'KEN', 160, 0, NULL),
(40, 'PAUL', '1998-03-19', 'ABYMES', '16AR73408', 'POINT', '2016-09-14', '2031-09-14', 'ERMIONE AUDREY', 185, 0, NULL),
(41, 'MARTY-BOSC', '1961-06-22', 'TOURNON', '790469110632', 'LYON', '1979-10-05', NULL, 'PASCALE', 248, 0, NULL),
(42, 'MARTY', '1958-06-30', 'ARBA', '22AQ01938', 'PREF 38', '2022-08-11', '2037-08-11', 'CLAUDE GERALD', 248, 0, NULL),
(45, 'ony', '1991-07-27', 'antsirabe', '1550d', 'majunga', '2015-05-12', '2028-04-12', 'rakoto', 117, 0, '0325072183');

-- --------------------------------------------------------

--
-- Structure de la table `contact`
--

DROP TABLE IF EXISTS `contact`;
CREATE TABLE IF NOT EXISTS `contact` (
  `id` int NOT NULL AUTO_INCREMENT,
  `mail` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `nom` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `telephone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `message` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `devis`
--

DROP TABLE IF EXISTS `devis`;
CREATE TABLE IF NOT EXISTS `devis` (
  `id` int NOT NULL AUTO_INCREMENT,
  `client_id` int NOT NULL,
  `vehicule_id` int DEFAULT NULL,
  `date_depart` datetime NOT NULL,
  `date_retour` datetime NOT NULL,
  `agence_depart` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `agence_retour` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `lieu_sejour` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `conducteur` tinyint(1) NOT NULL,
  `date_creation` datetime DEFAULT NULL,
  `duree` double NOT NULL,
  `prix` double NOT NULL,
  `numero` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `transformed` tinyint(1) DEFAULT NULL,
  `tarif_vehicule` double DEFAULT NULL,
  `prix_options` double NOT NULL,
  `prix_garanties` double NOT NULL,
  `stripe_session_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `download_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `payement_percentage` double DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_8B27C52B19EB6921` (`client_id`),
  KEY `IDX_8B27C52B4A4A3511` (`vehicule_id`)
) ENGINE=InnoDB AUTO_INCREMENT=252 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `devis`
--

INSERT INTO `devis` (`id`, `client_id`, `vehicule_id`, `date_depart`, `date_retour`, `agence_depart`, `agence_retour`, `lieu_sejour`, `conducteur`, `date_creation`, `duree`, `prix`, `numero`, `transformed`, `tarif_vehicule`, `prix_options`, `prix_garanties`, `stripe_session_id`, `download_id`, `payement_percentage`) VALUES
(135, 117, 1, '2021-11-19 20:11:00', '2021-12-05 20:11:00', 'AEROPORT DE POINT-A-PITRE', 'AEROPORT DE POINT-A-PITRE', 'test', 1, '2021-11-18 20:11:57', 2, 120, 'DV00001', 0, 0, 30, 90, NULL, NULL, NULL),
(136, 145, 3, '2022-02-02 21:05:00', '2022-02-17 21:05:00', 'AEROPORT DE POINT-A-PITRE', 'AEROPORT DE POINT-A-PITRE', 'MOULE', 1, '2022-02-02 04:08:57', 15, 510, 'DV00136', 0, 420, 0, 90, NULL, '61f9d9a9bea10', NULL),
(137, 146, 3, '2022-02-02 21:10:00', '2022-02-17 21:10:00', 'AEROPORT DE POINT-A-PITRE', 'AEROPORT DE POINT-A-PITRE', 'MOULE', 1, '2022-02-02 04:11:44', 15, 540, 'DV00137', 0, 420, 30, 90, NULL, '61f9da502c622', NULL),
(138, 146, 9, '2022-02-02 21:18:00', '2022-02-18 21:18:00', 'AEROPORT DE POINT-A-PITRE', 'AEROPORT DE POINT-A-PITRE', 'MOULE', 1, '2022-02-02 04:20:47', 16, 957, 'DV00138', 0, 837, 30, 90, NULL, '61f9dc6f1c2a0', NULL),
(139, 147, 1, '2022-02-03 14:00:00', '2022-02-06 14:00:00', 'AEROPORT DE POINT-A-PITRE', 'AEROPORT DE POINT-A-PITRE', 'Le moule', 1, '2022-02-02 16:01:43', 3, 210, 'DV00139', 0, 120, 0, 90, NULL, '61fa80b72f11a', NULL),
(140, 147, 13, '2022-02-04 16:24:00', '2022-02-06 16:24:00', 'AEROPORT DE POINT-A-PITRE', 'AEROPORT DE POINT-A-PITRE', 'Le moule', 1, '2022-02-03 18:27:33', 2, 210, 'DV00140', 0, 120, 0, 90, NULL, '61fbf465df766', NULL),
(141, 146, 2, '2022-02-04 18:43:00', '2022-02-10 18:44:00', 'AEROPORT DE POINT-A-PITRE', 'AEROPORT DE POINT-A-PITRE', 'aeoport pap', 1, '2022-02-04 01:45:28', 6, 302.5, 'DV00141', 0, 182.5, 30, 90, NULL, '61fc5b0867002', NULL),
(142, 117, 9, '2022-02-06 07:31:00', '2022-02-19 07:31:00', 'AEROPORT DE POINT-A-PITRE', 'AEROPORT DE POINT-A-PITRE', 'Hotel', 1, '2022-02-04 07:32:40', 13, 550, 'DV00142', 0, 420, 30, 100, NULL, '61fcac683d62a', NULL),
(143, 146, 8, '2022-02-05 08:19:00', '2022-02-06 07:20:00', 'AEROPORT DE POINT-A-PITRE', 'AEROPORT DE POINT-A-PITRE', 'MOULE', 1, '2022-02-04 14:23:24', 1, 203.7, 'DV00143', 0, 83.7, 30, 90, NULL, '61fd0cacaaea8', NULL),
(144, 146, 8, '2022-02-05 10:49:00', '2022-02-06 10:49:00', 'AEROPORT DE POINT-A-PITRE', 'AEROPORT DE POINT-A-PITRE', 'MOULE', 1, '2022-02-04 17:13:55', 1, 203.7, 'DV00144', 0, 83.7, 30, 90, NULL, '61fd34a360afc', NULL),
(145, 117, 2, '2022-02-12 11:17:00', '2022-02-20 11:17:00', 'AEROPORT DE POINT-A-PITRE', 'AEROPORT DE POINT-A-PITRE', 'test', 1, '2022-02-06 11:18:43', 8, 464, 'DV00145', 0, 364, 0, 100, NULL, '61ff8463b84f8', NULL),
(146, 117, 1, '2022-02-12 11:28:00', '2022-02-27 11:28:00', 'AGENCE DU MOULE', 'GARE MARITIME DE BERGERVIN', 'Hotel', 1, '2022-02-06 11:30:12', 15, 685, 'DV00146', 0, 585, 0, 100, NULL, '61ff8714732b9', NULL),
(147, 145, 2, '2022-02-13 09:36:00', '2022-02-20 09:36:00', 'AGENCE DU MOULE', 'AEROPORT DE POINT-A-PITRE', 'MOULE', 1, '2022-02-06 16:38:13', 7, 272.5, 'DV00147', 0, 182.5, 0, 90, NULL, '61ffcf454e330', NULL),
(148, 117, 1, '2022-02-11 08:18:00', '2022-02-25 08:18:00', 'AEROPORT DE POINT-A-PITRE', 'AEROPORT DE POINT-A-PITRE', 'test', 1, '2022-02-07 08:19:43', 14, 585, 'DV00148', 0, 585, 0, 0, NULL, '6200abefed032', NULL),
(149, 117, 8, '2022-02-09 08:19:00', '2022-02-24 08:19:00', 'AEROPORT DE POINT-A-PITRE', 'AEROPORT DE POINT-A-PITRE', 'test', 1, '2022-02-07 08:20:42', 15, 364, 'DV00149', 0, 364, 0, 0, NULL, '6200ac2ab8b5e', NULL),
(150, 147, 1, '2022-02-14 10:35:00', '2022-02-18 10:35:00', 'AGENCE DU MOULE', 'AEROPORT DE POINT-A-PITRE', 'hotel', 1, '2022-02-07 12:40:04', 4, 292, 'DV00150', 0, 292, 0, 0, NULL, '6200e8f4b720c', NULL),
(151, 146, 8, '2022-02-09 22:14:00', '2022-03-12 22:14:00', 'AGENCE DU MOULE', 'AEROPORT DE POINT-A-PITRE', 'MOULE', 1, '2022-02-08 05:16:38', 3, 83.7, 'DV00151', 0, 83.7, 0, 0, NULL, '6201d2863a0c1', NULL),
(152, 117, 2, '2022-02-14 15:08:00', '2022-02-15 15:08:00', 'AEROPORT DE POINT-A-PITRE', 'AEROPORT DE POINT-A-PITRE', 'Test', 1, '2022-02-13 15:28:36', 1, 203.7, 'DV00152', 0, 83.7, 30, 90, NULL, '6208f97412a1e', NULL),
(153, 117, 2, '2022-03-17 14:12:00', '2022-03-25 14:12:00', 'Gare maritime de Saint-François', 'Petit-canal', 'hjk', 1, '2022-03-07 14:13:50', 8, 385, 'DV00153', 0, 225, 60, 100, NULL, '6225e8ee9a4fa', NULL),
(154, 117, 2, '2022-03-26 14:24:00', '2022-03-27 14:24:00', 'Gare Maritime de Bergervin', 'Abymes', 'test', 1, '2022-03-21 14:26:10', 1, 205, 'DV00154', 0, 75, 30, 100, NULL, '623860d298dd0', NULL),
(155, 117, 2, '2022-03-25 14:31:00', '2022-03-26 14:32:00', 'Gare Maritime de Bergervin', 'Sainte-anne', 'test', 1, '2022-03-24 14:33:41', 1, 205, 'DV00155', 0, 75, 30, 100, NULL, '623c5715f21a0', NULL),
(156, 117, 2, '2022-03-26 14:37:00', '2022-03-27 14:38:00', 'Gare Maritime de Bergervin', 'Sainte-anne', 'Hotel', 1, '2022-03-24 14:39:14', 1, 195, 'DV00156', 0, 75, 30, 90, NULL, '623c5862acca5', NULL),
(157, 147, 8, '2022-04-05 10:18:00', '2022-04-09 10:18:00', 'Agence du Moule', 'Agence du Moule', 'Le moule', 1, '2022-03-28 11:20:01', 4, 108.7, 'DV00157', 0, 108.7, 0, 0, NULL, '62416fb1e2121', NULL),
(158, 117, 12, '2022-04-09 12:56:00', '2022-04-10 12:56:00', 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 'test', 1, '2022-04-08 12:57:22', 1, 190, 'DV00158', 0, 60, 30, 100, NULL, '625007027cc63', NULL),
(159, 117, 11, '2022-04-13 10:25:00', '2022-04-24 10:25:00', 'Gare Maritime de Bergervin', 'Pointe-à-pitre', 'test', 1, '2022-04-12 10:27:53', 11, 395, 'DV00159', 0, 275, 30, 90, NULL, '625529f92d681', NULL),
(160, 147, 11, '2022-04-13 10:00:00', '2022-04-20 21:05:00', 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 'Le moule', 1, '2022-04-12 22:06:41', 8, 275, 'DV00160', 0, 275, 0, 0, NULL, '6255cdc125260', NULL),
(161, 117, 3, '2022-04-22 11:09:00', '2022-04-23 11:09:00', 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 'fds', 1, '2022-04-14 11:10:56', 1, 90, 'DV00161', 0, 60, 30, 0, NULL, '6257d7102685e', NULL),
(162, 147, 3, '2022-04-20 11:00:00', '2022-04-23 11:00:00', 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 'Le moule', 0, '2022-04-14 12:17:45', 3, 60, 'DV00162', 0, 60, 0, 0, 'cs_test_a14IuPGXnd6hX8GN0atiR8RNNCB9lEOWkxIFKWspYrABm8SdwwexmsXuNG', '6257e6b990269', 100),
(163, 147, 2, '2022-05-23 14:15:00', '2022-05-24 14:15:00', 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 'hotel', 0, '2022-05-21 15:16:27', 1, 5, 'DV00163', 1, 5, 0, 0, 'cs_live_a12In4ymXg0M0k3DFkZB8Mbj4hYByc6EEUVabs57BwV3KnjtBYxeMZidq5', '6288d81bb7edb', 25),
(164, 147, 12, '2022-06-11 10:00:00', '2022-06-12 10:00:00', 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 'hotel', 1, '2022-06-10 09:43:48', 1, 45, 'DV00164', 0, 45, 0, 0, NULL, '62a2e82415cee', 50),
(165, 147, 12, '2022-06-18 13:30:00', '2022-06-22 13:30:00', 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 'hotel', 1, '2022-06-14 14:42:25', 4, 112.5, 'DV00165', 0, 112.5, 0, 0, NULL, '62a874216516c', 50),
(166, 147, 3, '2022-09-01 08:00:00', '2022-09-30 16:00:00', 'Agence du Moule', 'Agence du Moule', 'MOULE', 1, '2022-08-07 05:12:10', 30, 700, 'DV2200166', 0, 700, 0, 0, NULL, '62ef1f7ac5ff0', NULL),
(167, 149, 2, '2022-08-12 12:46:00', '2022-08-16 14:46:00', 'Gare Maritime de Bergervin', 'Gare Maritime de Bergervin', 'Mety sa tsia', 1, '2022-08-07 05:52:00', 5, 182, 'DV2200167', 0, 152, 30, 0, NULL, '62ef28d0c61f4', NULL),
(168, 180, 3, '2022-08-19 22:53:00', '2022-08-28 16:53:00', 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 'Mety sa tsia', 1, '2022-08-07 05:54:55', 9, 405, 'DV2200168', 0, 375, 30, 0, NULL, '62ef297f602fe', NULL),
(169, 184, 10, '2022-09-11 14:00:00', '2022-09-25 14:00:00', 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 'MOULE', 0, '2022-08-17 23:56:10', 14, 280, 'DV2200169', 0, 280, 0, 0, NULL, '62fd55eaeeb63', NULL),
(170, 180, 2, '2024-01-02 18:42:00', '2024-01-31 18:43:00', 'Gosier', 'Gosier', 'gosier', 1, '2023-12-23 01:44:57', 29, 950, 'DV2300170', 1, 900, 0, 0, NULL, '65861169a92ba', NULL),
(171, 180, 3, '2024-01-21 08:56:00', '2024-02-11 08:56:00', 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 'moule', 1, '2024-01-18 15:57:00', 21, 980, 'DV2400171', 0, 930, 0, 0, NULL, '65a9201caec58', NULL),
(172, 270, 2, '2024-07-30 10:15:00', '2024-08-28 10:15:00', 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 'PAP', 0, '2024-03-14 17:20:42', 29, 579, 'DV2400172', 0, 579, 0, 0, NULL, '65f307ba7eb96', NULL),
(173, 180, 18, '2024-04-15 11:01:00', '2024-04-19 11:01:00', 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 'moule', 0, '2024-04-12 18:02:33', 4, 463, 'DV2400173', 0, 463, 0, 0, NULL, '66194d09bdbac', NULL),
(174, 180, 3, '2024-11-28 12:21:00', '2024-12-06 12:22:00', 'Moule', 'Moule', 'moule', 0, '2024-08-27 19:23:17', 8, 319, 'DV2400174', 0, 319, 0, 0, NULL, '66cdfd7500e12', NULL),
(175, 284, 13, '2025-01-05 14:18:00', '2025-01-20 14:19:00', 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 'PAP', 0, '2024-09-21 16:22:04', 15, 530, 'DV2400175', 0, 530, 0, 0, NULL, '66eec87c138a7', NULL),
(176, 284, 13, '2025-01-07 14:27:00', '2025-01-20 14:27:00', 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 'PAP', 0, '2024-09-21 16:29:28', 13, 530, 'DV2400176', 1, 530, 0, 0, NULL, '66eeca38602aa', NULL),
(177, 295, 11, '2024-11-03 11:50:00', '2024-11-10 11:51:00', 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 'moule', 0, '2024-09-25 18:55:05', 7, 130, 'DV2400177', 0, 130, 0, 0, NULL, '66f4325951a57', NULL),
(178, 295, 11, '2024-11-03 08:00:00', '2024-11-10 17:17:00', 'Agence du Moule', 'Agence du Moule', 'moule', 0, '2024-09-27 00:20:22', 7, 130, 'DV2400178', 1, 130, 0, 0, NULL, '66f5d016ecf3d', NULL),
(179, 117, 18, '2024-10-14 12:00:00', '2024-10-18 12:00:00', 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 'tes', 1, '2024-10-13 11:15:39', 4, 90, 'DV2400179', 1, 40, 50, 0, NULL, '670b81ab106e5', NULL),
(180, 117, 17, '2024-10-14 12:12:00', '2024-10-28 13:00:00', 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 'test', 1, '2024-10-13 12:35:03', 14, 190, 'DV2400180', 0, 140, 50, 0, NULL, '670b9447a1ac9', NULL),
(181, 117, 17, '2024-11-19 12:00:00', '2024-11-28 12:00:00', 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 'test', 1, '2024-11-18 17:06:57', 9, 180, 'DV2400181', 0, 100, 80, 0, NULL, '673b4a0190691', NULL),
(182, 117, 17, '2024-11-19 12:00:00', '2024-11-21 13:00:00', 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 'test', 1, '2024-11-18 17:17:45', 2, 100, 'DV2400182', 0, 20, 80, 0, NULL, '673b4c89d303b', NULL),
(183, 117, 17, '2024-11-19 12:00:00', '2024-11-27 15:00:00', 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 'test', 1, '2024-11-18 17:20:09', 8, 90, 'DV2400183', 0, 10, 80, 0, NULL, '673b4d19f289c', NULL),
(184, 117, 17, '2024-11-22 12:00:00', '2024-11-26 15:00:00', 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 'test', 1, '2024-11-21 18:03:04', 4, 350, 'DV2400184', 0, 210, 50, 90, NULL, '673f4ba8627a5', NULL),
(185, 117, 16, '2024-11-25 12:00:00', '2024-12-05 14:00:00', 'Morne-à-l\'Eau', 'Port-louis', 'hotel', 1, '2024-11-24 11:39:24', 2, 500, 'DV2400185', 0, 100, 50, 350, NULL, '6742e63c43707', NULL),
(186, 117, 17, '2024-11-28 12:00:00', '2024-12-01 12:00:00', 'Gosier', 'Pointe-à-pitre', 'test', 1, '2024-11-27 17:50:48', 3, 590, 'DV2400186', 0, 100, 140, 350, NULL, '674731c807693', NULL),
(187, 117, 17, '2024-11-28 19:16:00', '2024-12-08 19:16:00', 'Gare Maritime de Bergervin', 'Aéroport de Point-à-pitre', 'test', 1, '2024-11-27 19:45:02', 10, 590, 'DV2400187', 1, 20, 220, 350, NULL, '67474c8e128c1', NULL),
(188, 117, 18, '2024-12-03 12:00:00', '2024-12-10 13:00:00', 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 'test', 1, '2024-12-02 17:36:44', 7, 430, 'DV2400188', 0, 210, 220, 0, NULL, '674dc5fc2d690', NULL),
(189, 117, 18, '2024-12-10 12:00:00', '2024-12-12 12:00:00', 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 'test', 1, '2024-12-09 19:17:30', 2, 280, 'DV2400189', 1, 100, 80, 100, NULL, '6757181a8b4bc', NULL),
(190, 117, 17, '2024-12-10 12:00:00', '2024-12-16 12:00:00', 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 'test', 0, '2024-12-09 19:21:53', 6, 210, 'DV2400190', 0, 210, 0, 0, NULL, '67571921987bd', NULL),
(191, 117, 17, '2024-12-11 12:00:00', '2024-12-12 23:00:00', 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 'test', 0, '2024-12-10 21:17:17', 1, 290, 'DV2400191', 1, 100, 30, 160, NULL, '675885adc5117', NULL),
(194, 117, 18, '2024-12-23 12:00:00', '2024-12-24 12:00:00', 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', NULL, 0, '2024-12-22 10:33:40', 1, 220, 'DV2400192', 1, 100, 120, 0, NULL, '6767c0d48b99c', NULL),
(195, 117, 18, '2024-12-23 12:00:00', '2024-12-31 12:00:00', 'Moule', 'Port-louis', NULL, 0, '2024-12-22 11:39:34', 8, 890, 'DV2400195', 1, 450, 90, 350, NULL, '6767d046dfeec', NULL),
(196, 117, 18, '2024-12-23 12:48:00', '2024-12-29 12:48:00', 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 'test', 0, '2024-12-22 12:50:01', 6, 650, 'DV2400196', 1, 210, 90, 350, NULL, '6767e0c9c8166', NULL),
(197, 117, 17, '2024-12-24 09:46:00', '2024-12-28 09:46:00', 'Agence du Moule', 'Aéroport de Point-à-pitre', 'rzqr', 0, '2024-12-23 09:47:18', 4, 850, 'DV2400197', 0, 210, 290, 350, NULL, '676907769b601', NULL),
(198, 117, 17, '2024-12-24 09:56:00', '2024-12-27 09:56:00', 'Gare Maritime de Bergervin', 'Sainte-anne', 'test', 0, '2024-12-23 10:08:14', 3, 710, 'DV2400198', 0, 100, 260, 350, NULL, '67690c5e3bc9c', NULL),
(199, 117, 17, '2024-12-25 10:13:00', '2024-12-28 10:13:00', 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 'test', 0, '2024-12-23 10:14:08', 3, 390, 'DV2400199', 0, 100, 290, 0, NULL, '67690dc0ede63', NULL),
(200, 117, 17, '2024-12-25 10:27:00', '2024-12-28 10:27:00', 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 'test', 0, '2024-12-23 10:53:38', 3, 740, 'DV2400200', 1, 100, 290, 350, NULL, '676917021cc48', NULL),
(201, 117, 16, '2024-12-25 12:35:00', '2025-01-05 12:35:00', 'Aéroport de Point-à-pitre', 'Pointe-à-pitre', 'test', 0, '2024-12-23 12:37:22', 11, 1030, 'DV2400201', 0, 450, 230, 350, NULL, '67692f52d2a7b', NULL),
(202, 117, 16, '2024-12-27 21:01:00', '2025-01-05 21:02:00', 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 'tes', 0, '2024-12-23 21:02:23', 9, 570, 'DV2400202', 1, 450, 120, 0, NULL, '6769a5afb715e', NULL),
(203, 117, 17, '2024-12-30 12:00:00', '2024-12-31 12:00:00', 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 'test', 0, '2024-12-29 19:45:14', 1, 410, 'DV2400203', 0, 100, 210, 100, NULL, '67717c9a4a82b', NULL),
(204, 117, 18, '2024-12-31 17:27:00', '2025-01-03 17:27:00', 'Gare Maritime de Bergervin', 'Gosier', 'test', 0, '2024-12-30 17:28:35', 3, 510, 'DV2400204', 0, 100, 150, 260, NULL, '6772ae1376f40', NULL),
(205, 117, 18, '2025-01-08 12:00:00', '2025-01-09 12:00:00', 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 'test', 0, '2025-01-06 19:56:30', 1, 320, 'DV2500205', 0, 100, 120, 100, NULL, '677c0b3e7422b', NULL),
(206, 117, 18, '2025-01-08 12:00:00', '2025-01-21 14:00:00', 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 'test', 0, '2025-01-07 18:05:05', 13, 780, 'DV2500206', 0, 450, 140, 190, NULL, '677d42a1ad7e1', NULL),
(207, 300, 18, '2025-01-20 12:00:00', '2025-01-21 12:00:00', 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 'test', 0, '2025-01-09 21:18:01', 1, 100, 'DV2500207', 0, 100, 0, 0, NULL, '678012d9edc9e', NULL),
(208, 301, 18, '2025-03-06 12:00:00', '2025-03-12 12:00:00', 'Agence du Moule', 'Anse-bertrand', 'test', 0, '2025-03-05 17:52:44', 6, 360, 'DV2500208', 0, 210, 60, 90, NULL, '67c8653c18ab3', NULL),
(209, 117, 18, '2025-03-10 12:00:00', '2025-03-24 12:00:00', 'Aéroport de Point-à-pitre', 'Sainte-anne', 'test', 0, '2025-03-09 16:40:33', 14, 510, 'DV2500209', 0, 450, 60, 0, NULL, '67cd9a51af1bb', NULL),
(210, 117, 18, '2025-03-21 17:37:00', '2025-03-29 17:37:00', 'Morne-à-l\'Eau', 'Port-louis', 'rzswr', 0, '2025-03-20 17:37:39', 8, 480, 'DV2500210', 0, 450, 30, 0, NULL, '67dc2833ccc66', NULL),
(211, 117, 18, '2025-04-03 12:13:00', '2025-05-03 12:13:00', 'Sainte-anne', 'Aéroport de Point-à-pitre', 'test', 0, '2025-03-31 12:13:40', 30, 890, 'DV2500211', 0, 800, 90, 0, NULL, '67ea5cc42e5f9', NULL),
(212, 117, 18, '2025-04-07 19:23:00', '2025-06-08 19:23:00', 'Port-louis', 'Morne-à-l\'Eau', 'test', 0, '2025-04-03 19:23:57', 62, 800, 'DV2500212', 0, 800, 0, 0, NULL, '67eeb61d36404', NULL),
(213, 117, 18, '2025-04-09 12:00:00', '2025-04-30 12:00:00', 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 'test', 0, '2025-04-08 17:50:45', 21, 860, 'DV2500213', 0, 800, 60, 0, NULL, '67f537c5e3030', NULL),
(214, 117, 18, '2025-04-15 12:00:00', '2025-06-06 12:00:00', 'Morne-à-l\'Eau', 'Sainte-anne', 'test', 0, '2025-04-10 18:15:27', 52, 1020, 'DV2500214', 0, 800, 30, 190, NULL, '67f7e08f6aa9d', NULL),
(215, 117, 18, '2025-04-16 12:00:00', '2025-04-30 13:00:00', 'Morne-à-l\'Eau', 'Petit-canal', 'test', 0, '2025-04-15 19:51:26', 14, 570, 'DV2500215', 1, 450, 120, 0, NULL, '67fe8e8e0d03f', NULL),
(216, 117, 17, '2025-04-18 12:00:00', '2025-04-20 12:00:00', 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 'test', 0, '2025-04-17 17:45:40', 2, 190, 'DV2500216', 1, 100, 90, 0, NULL, '680114146024f', NULL),
(217, 117, 16, '2025-04-18 14:00:00', '2025-04-30 14:00:00', 'Morne-à-l\'Eau', 'Port-louis', 'test', 0, '2025-04-17 17:57:52', 12, 540, 'DV2500217', 1, 450, 0, 90, NULL, '680116f0452cc', NULL),
(218, 117, 17, '2025-04-23 12:00:00', '2025-05-02 12:00:00', 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 'test', 0, '2025-04-20 14:45:15', 9, 450, '*********', 1, 450, 0, 0, NULL, '6804de4b61ef5', NULL),
(219, 117, 15, '2025-04-21 12:00:00', '2025-04-29 13:00:00', 'Morne-à-l\'Eau', 'Aéroport de Point-à-pitre', 'tes', 0, '2025-04-20 16:21:39', 8, 450, '*********', 1, 450, 0, 0, NULL, '6804f4e31c5a7', NULL),
(220, 117, 13, '2025-04-21 12:00:00', '2025-04-29 12:00:00', 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 'test', 0, '2025-04-20 16:40:39', 8, 584, '*********', 0, 554, 30, 0, NULL, '6804f95797241', NULL),
(221, 117, 12, '2025-04-22 12:00:00', '2025-04-25 13:00:00', 'Morne-à-l\'Eau', 'Aéroport de Point-à-pitre', 'test', 0, '2025-04-21 18:06:38', 3, 180, 'DV2500221', 0, 60, 120, 0, NULL, '68065efebe227', NULL),
(222, 117, 12, '2025-04-22 12:00:00', '2025-04-30 13:00:00', 'Gare maritime de Saint-François', 'Saint-François', 'test', 0, '2025-04-21 18:08:25', 8, 395, 'DV2500222', 0, 275, 120, 0, NULL, '68065f694706a', NULL),
(223, 117, 12, '2025-04-22 12:00:00', '2025-04-23 12:00:00', 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 'test', 0, '2025-04-21 18:21:49', 1, 60, 'DV2500223', 0, 60, 0, 0, NULL, '6806628d9b180', NULL),
(224, 117, 12, '2025-04-22 12:00:00', '2025-04-29 13:00:00', 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 'test', 0, '2025-04-21 18:40:34', 7, 137, 'DV2500224', 0, 137, 0, 0, NULL, '680666f21017e', NULL),
(225, 117, 12, '2025-04-22 12:00:00', '2025-04-25 12:00:00', 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 'test', 0, '2025-04-21 18:49:02', 3, 90, 'DV2500225', 0, 60, 30, 0, NULL, '680668eeda7dd', NULL),
(226, 117, 11, '2025-04-22 12:00:00', '2025-04-25 11:00:00', 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 'tt', 0, '2025-04-21 18:56:18', 3, 60, '*********', 0, 60, 0, 0, NULL, '68066aa24300a', NULL),
(227, 117, 12, '2025-04-22 12:00:00', '2025-04-30 12:00:00', 'Moule', 'Pointe-à-pitre', 'test', 0, '2025-04-21 18:58:06', 8, 275, 'DV2500227', 1, 275, 0, 0, NULL, '68066b0e3f0e8', NULL),
(228, 117, 11, '2025-04-22 12:00:00', '2025-04-24 14:00:00', 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 'test', 0, '2025-04-21 19:01:27', 2, 90, 'DV2500228', 0, 60, 30, 0, NULL, '68066bd7233f9', NULL),
(229, 117, 11, '2025-04-22 01:02:00', '2025-04-24 12:00:00', 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 'test', 0, '2025-04-21 19:18:29', 2, 90, 'DV2500229', 0, 60, 30, 0, NULL, '68066fd58f8fd', NULL),
(230, 117, 11, '2025-04-22 12:00:00', '2025-04-30 14:00:00', 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 'test', 0, '2025-04-21 19:22:51', 8, 435, 'DV2500230', 0, 275, 60, 100, NULL, '680670dbb404a', NULL),
(231, 302, 11, '2025-04-25 12:00:00', '2025-04-30 13:00:00', 'Moule', 'Aéroport de Point-à-pitre', 'test', 0, '2025-04-24 16:53:13', 5, 197, '*********', 0, 137, 60, 0, NULL, '680a4249aabe6', NULL),
(232, 373, 18, '2025-05-13 12:00:00', '2025-05-29 12:00:00', 'Moule', 'Port-louis', 'test', 0, '2025-05-09 13:00:56', 16, 925, 'DV2500232', 1, 800, 125, 0, NULL, '681dd25852363', NULL),
(233, 375, 17, '2025-05-17 12:00:00', '2025-05-19 12:00:00', 'Gosier', 'Sainte-anne', 'gdxg', 0, '2025-05-12 19:21:40', 2, 100, 'DV2500233', 1, 100, 0, 0, NULL, '68222014e1c0c', NULL),
(234, 117, 17, '2025-05-21 12:00:00', '2025-05-29 12:03:00', 'Morne-à-l\'Eau', 'Aéroport de Point-à-pitre', 'test', 1, '2025-05-20 18:15:32', 8, 720, 'DV2500234', 1, 450, 170, 100, NULL, '682c9c94c6f45', NULL),
(235, 117, 16, '2025-05-27 12:00:00', '2025-05-28 13:00:00', 'Moule', 'Aéroport de Point-à-pitre', 'test', 0, '2025-05-26 17:41:30', 1, 220, 'DV2500235', 0, 100, 120, 0, NULL, '68347d9a0b858', NULL),
(236, 117, 18, '2025-06-06 12:00:00', '2025-06-11 13:00:00', 'Petit-canal', 'Port-louis', 'test', 0, '2025-06-05 18:36:47', 5, 210, 'DV2500236', 0, 210, 0, 0, NULL, '6841b98f47246', NULL),
(237, 117, 18, '2025-06-20 16:54:00', '2025-06-28 16:54:00', 'Agence du Moule', 'Pointe-à-pitre', 'test', 0, '2025-06-19 17:05:13', 8, 615, 'DV2500237', 0, 500, 115, 0, NULL, '68541919dad78', NULL),
(238, 117, 18, '2025-06-25 18:28:00', '2025-06-29 18:28:00', 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 'test', 0, '2025-06-23 18:28:45', 4, 210, 'DV2500238', 0, 210, 0, 0, NULL, '685972adb69fa', NULL),
(239, 117, 18, '2025-06-27 18:19:00', '2025-07-04 18:19:00', 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', NULL, 1, '2025-06-24 18:19:40', 7, 685, 'DV2500239', 0, 210, 285, 190, NULL, '685ac20c2fd51', NULL),
(240, 117, 18, '2025-06-25 18:36:00', '2025-06-30 18:36:00', 'Moule', 'Sainte-anne', 'test', 1, '2025-06-24 18:38:49', 5, 875, 'DV2500240', 1, 210, 315, 350, NULL, '685ac689a481f', NULL),
(241, 117, 17, '2025-06-26 11:36:00', '2025-07-06 11:36:00', 'Gare Maritime de Bergervin', 'Petit-canal', 'test', 0, '2025-06-26 11:39:46', 10, 500, 'DV2500241', 0, 500, 0, 0, NULL, '685d0752104d8', NULL),
(242, 117, 17, '2025-06-26 13:21:00', '2025-06-29 13:21:00', 'Agence du Moule', 'Anse-bertrand', 'test', 0, '2025-06-26 13:22:19', 3, 100, 'DV2500242', 0, 100, 0, 0, NULL, '685d1f5bc9fdc', NULL),
(243, 117, 17, '2025-06-28 13:40:00', '2025-07-06 13:40:00', 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', NULL, 0, '2025-06-26 13:41:15', 8, 510, 'DV2500243', 0, 450, 60, 0, NULL, '685d23cbf1724', NULL),
(244, 117, 18, '2025-07-03 20:32:00', '2025-07-06 20:32:00', 'Morne-à-l\'Eau', 'Aéroport de Point-à-pitre', 'test', 0, '2025-07-03 20:33:38', 3, 160, 'DV2500244', 0, 100, 60, 0, NULL, '6866bef276bfe', NULL),
(245, 117, 18, '2025-07-31 20:41:00', '2025-08-30 20:41:00', 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 'test', 0, '2025-07-03 20:41:34', 30, 920, 'DV2500245', 1, 800, 120, 0, NULL, '6866c0ce8782e', NULL),
(246, 117, 18, '2025-07-10 19:17:00', '2025-07-17 19:17:00', 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 'test', 0, '2025-07-10 19:17:57', 7, 300, 'DV2500246', 1, 210, 90, 0, NULL, '686fe7b5333d2', NULL),
(247, 117, 17, '2025-07-10 20:59:00', '2025-07-11 20:59:00', 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', NULL, 0, '2025-07-10 20:59:45', 1, 130, 'DV2500247', 1, 100, 30, 0, NULL, '686fff91d8574', NULL),
(248, 117, 16, '2025-07-10 21:11:00', '2025-07-11 21:11:00', 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', NULL, 0, '2025-07-10 21:11:59', 1, 100, 'DV2500248', 1, 100, 0, 0, NULL, '6870026fbef05', NULL),
(249, 117, 15, '2025-07-10 21:20:00', '2025-07-11 21:20:00', 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', NULL, 0, '2025-07-10 21:20:50', 1, 100, 'DV2500249', 1, 100, 0, 0, NULL, '68700482b29d7', NULL),
(250, 117, 18, '2025-07-18 19:22:00', '2025-07-24 19:22:00', 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', NULL, 0, '2025-07-17 19:36:32', 6, 270, 'DV2500250', 1, 210, 60, 0, NULL, '68792690ba247', NULL),
(251, 117, 18, '2025-07-19 19:38:00', '2025-07-27 19:38:00', 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', NULL, 0, '2025-07-17 19:38:21', 8, 450, 'DV2500251', 0, 450, 0, 0, NULL, '687926fd3891f', NULL);

-- --------------------------------------------------------

--
-- Structure de la table `devis_garantie`
--

DROP TABLE IF EXISTS `devis_garantie`;
CREATE TABLE IF NOT EXISTS `devis_garantie` (
  `devis_id` int NOT NULL,
  `garantie_id` int NOT NULL,
  PRIMARY KEY (`devis_id`,`garantie_id`),
  KEY `IDX_13DC356C41DEFADA` (`devis_id`),
  KEY `IDX_13DC356CA4B9602F` (`garantie_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `devis_garantie`
--

INSERT INTO `devis_garantie` (`devis_id`, `garantie_id`) VALUES
(135, 4),
(136, 4),
(137, 4),
(138, 4),
(139, 4),
(140, 4),
(141, 4),
(142, 5),
(143, 4),
(144, 4),
(145, 5),
(146, 5),
(147, 4),
(152, 4),
(153, 5),
(154, 5),
(155, 5),
(156, 4),
(158, 5),
(159, 4),
(184, 4),
(185, 4),
(185, 5),
(185, 6),
(186, 4),
(186, 5),
(186, 6),
(187, 4),
(187, 5),
(187, 6),
(189, 5),
(191, 6),
(195, 4),
(195, 5),
(195, 6),
(196, 4),
(196, 5),
(196, 6),
(197, 4),
(197, 5),
(197, 6),
(198, 4),
(198, 5),
(198, 6),
(200, 4),
(200, 5),
(200, 6),
(201, 4),
(201, 5),
(201, 6),
(203, 5),
(204, 5),
(204, 6),
(205, 5),
(206, 4),
(206, 5),
(208, 4),
(214, 4),
(214, 5),
(217, 4),
(230, 5),
(234, 5),
(239, 4),
(239, 5),
(240, 4),
(240, 5),
(240, 6);

-- --------------------------------------------------------

--
-- Structure de la table `devis_option`
--

DROP TABLE IF EXISTS `devis_option`;
CREATE TABLE IF NOT EXISTS `devis_option` (
  `id` int NOT NULL AUTO_INCREMENT,
  `devis_id` int DEFAULT NULL,
  `opt_id` int DEFAULT NULL,
  `quantity` int NOT NULL,
  `reservation_id` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_6693C77C41DEFADA` (`devis_id`),
  KEY `IDX_6693C77CCCEFD70A` (`opt_id`),
  KEY `IDX_6693C77CB83297E7` (`reservation_id`)
) ENGINE=InnoDB AUTO_INCREMENT=339 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `devis_option`
--

INSERT INTO `devis_option` (`id`, `devis_id`, `opt_id`, `quantity`, `reservation_id`) VALUES
(24, 194, 2, 2, NULL),
(25, 194, 3, 2, NULL),
(26, 194, 4, 1, NULL),
(52, 195, 2, 2, NULL),
(53, 195, 3, 1, NULL),
(58, 196, 2, 2, NULL),
(59, 196, 4, 1, NULL),
(60, 196, 6, 1, NULL),
(61, NULL, 2, 2, 391),
(62, NULL, 3, 2, 391),
(63, NULL, 4, 1, 391),
(64, NULL, 2, 2, 392),
(65, NULL, 3, 2, 392),
(66, NULL, 4, 1, 392),
(67, 200, 2, 3, NULL),
(68, 200, 3, 3, NULL),
(69, 200, 5, 1, NULL),
(70, 200, 6, 1, NULL),
(88, NULL, 2, 3, 393),
(89, NULL, 3, 3, 393),
(90, NULL, 4, 1, 393),
(91, NULL, 5, 1, 393),
(92, NULL, 6, 1, 393),
(96, NULL, 2, 3, 397),
(97, NULL, 3, 3, 397),
(98, NULL, 6, 1, 397),
(99, 202, 2, 2, NULL),
(100, 202, 3, 2, NULL),
(103, NULL, 2, 2, 398),
(104, NULL, 3, 2, 398),
(105, 135, 2, 1, NULL),
(106, 136, 4, 1, NULL),
(107, 137, 2, 1, NULL),
(108, 138, 3, 1, NULL),
(109, 139, 4, 1, NULL),
(110, 140, 4, 1, NULL),
(111, 141, 2, 1, NULL),
(112, 142, 3, 1, NULL),
(113, 143, 2, 1, NULL),
(114, 144, 2, 1, NULL),
(115, 152, 3, 1, NULL),
(116, 152, 4, 1, NULL),
(117, 153, 2, 1, NULL),
(118, 153, 6, 1, NULL),
(119, 154, 3, 1, NULL),
(120, 155, 2, 1, NULL),
(121, 156, 3, 1, NULL),
(122, 158, 2, 1, NULL),
(123, 159, 2, 1, NULL),
(124, 161, 2, 1, NULL),
(125, 167, 6, 1, NULL),
(126, 168, 6, 1, NULL),
(127, 180, 4, 1, NULL),
(128, 181, 2, 1, NULL),
(129, 182, 2, 1, NULL),
(130, 183, 3, 1, NULL),
(131, 186, 2, 1, NULL),
(132, 186, 3, 1, NULL),
(133, 186, 4, 1, NULL),
(134, 186, 6, 1, NULL),
(135, 187, 2, 1, NULL),
(136, 187, 3, 1, NULL),
(137, 187, 4, 1, NULL),
(138, 187, 5, 1, NULL),
(139, 187, 6, 1, NULL),
(140, 188, 2, 1, NULL),
(141, 188, 3, 1, NULL),
(142, 188, 4, 1, NULL),
(143, 188, 5, 1, NULL),
(144, 188, 6, 1, NULL),
(145, 189, 2, 1, NULL),
(146, 191, 3, 1, NULL),
(147, 195, 4, 1, NULL),
(148, 195, 5, 1, NULL),
(149, 195, 6, 1, NULL),
(150, NULL, 2, 1, 20),
(151, NULL, 2, 1, 21),
(152, NULL, 2, 1, 22),
(153, NULL, 3, 1, 22),
(154, NULL, 3, 1, 23),
(155, NULL, 2, 1, 24),
(156, NULL, 4, 1, 26),
(157, NULL, 2, 1, 27),
(158, NULL, 5, 1, 29),
(159, NULL, 2, 1, 31),
(160, NULL, 4, 1, 47),
(161, NULL, 4, 1, 61),
(162, NULL, 2, 1, 63),
(163, NULL, 5, 1, 63),
(164, NULL, 6, 1, 63),
(165, NULL, 2, 1, 72),
(166, NULL, 2, 1, 74),
(167, NULL, 2, 1, 384),
(168, NULL, 3, 1, 384),
(169, NULL, 4, 1, 384),
(170, NULL, 5, 1, 384),
(171, NULL, 6, 1, 384),
(177, NULL, 2, 1, 386),
(178, NULL, 2, 1, 387),
(179, NULL, 3, 1, 388),
(180, NULL, 4, 1, 390),
(181, NULL, 5, 1, 390),
(182, NULL, 6, 1, 390),
(183, NULL, 2, 3, 385),
(184, NULL, 3, 3, 385),
(185, NULL, 4, 1, 385),
(186, NULL, 5, 1, 385),
(187, NULL, 6, 1, 385),
(188, 201, 2, 2, NULL),
(189, 201, 3, 2, NULL),
(190, 201, 4, 1, NULL),
(191, 201, 5, 1, NULL),
(192, 201, 6, 1, NULL),
(193, 203, 2, 3, NULL),
(194, 203, 3, 3, NULL),
(195, 203, 4, 1, NULL),
(196, 203, 6, 1, NULL),
(207, 204, 2, 1, NULL),
(208, 204, 3, 3, NULL),
(209, 204, 6, 1, NULL),
(210, 205, 2, 2, NULL),
(211, 205, 3, 2, NULL),
(212, 205, 4, 1, NULL),
(213, 206, 2, 1, NULL),
(214, 206, 3, 1, NULL),
(215, 206, 5, 1, NULL),
(216, NULL, 2, 3, 399),
(217, NULL, 3, 3, 399),
(218, NULL, 4, 1, 399),
(219, NULL, 5, 1, 399),
(220, NULL, 6, 1, 399),
(221, 208, 3, 2, NULL),
(222, 209, 2, 1, NULL),
(223, 209, 3, 1, NULL),
(225, 210, 3, 1, NULL),
(230, 211, 2, 3, NULL),
(233, 213, 2, 1, NULL),
(234, 213, 3, 1, NULL),
(237, 214, 2, 1, NULL),
(240, 215, 2, 2, NULL),
(241, 215, 3, 2, NULL),
(242, NULL, 2, 2, 400),
(243, NULL, 3, 2, 400),
(244, NULL, 2, 2, 401),
(245, NULL, 3, 2, 401),
(246, NULL, 2, 2, 402),
(247, NULL, 3, 2, 402),
(248, NULL, 2, 2, 403),
(249, NULL, 3, 2, 403),
(250, NULL, 2, 2, 404),
(251, NULL, 3, 2, 404),
(252, NULL, 2, 2, 405),
(253, NULL, 3, 2, 405),
(254, NULL, 2, 2, 406),
(255, NULL, 3, 2, 406),
(256, NULL, 2, 2, 407),
(257, NULL, 3, 2, 407),
(259, 216, 2, 2, NULL),
(260, 216, 3, 1, NULL),
(261, NULL, 2, 2, 408),
(262, NULL, 3, 1, 408),
(263, 220, 2, 1, NULL),
(264, 221, 2, 2, NULL),
(265, 221, 3, 2, NULL),
(266, 222, 2, 2, NULL),
(267, 222, 3, 2, NULL),
(268, 225, 2, 1, NULL),
(270, 228, 2, 1, NULL),
(271, 229, 2, 1, NULL),
(272, 230, 2, 1, NULL),
(273, 230, 3, 1, NULL),
(276, 231, 2, 1, NULL),
(277, 231, 3, 1, NULL),
(282, 232, 2, 2, NULL),
(283, 232, 3, 1, NULL),
(284, 232, 4, 1, NULL),
(285, 232, 6, 1, NULL),
(286, NULL, 2, 2, 417),
(287, NULL, 3, 1, 417),
(288, NULL, 4, 1, 417),
(289, NULL, 6, 1, 417),
(290, 234, 2, 2, NULL),
(291, 234, 3, 2, NULL),
(292, NULL, 2, 2, 419),
(293, NULL, 3, 2, 419),
(294, 235, 2, 2, NULL),
(295, 235, 3, 2, NULL),
(296, 237, 4, 1, NULL),
(297, 237, 5, 1, NULL),
(298, 237, 6, 1, NULL),
(299, 239, 2, 2, NULL),
(300, 239, 3, 2, NULL),
(301, 239, 5, 1, NULL),
(302, 239, 6, 1, NULL),
(307, 240, 2, 3, NULL),
(308, 240, 3, 2, NULL),
(309, 240, 5, 1, NULL),
(310, 240, 6, 1, NULL),
(311, NULL, 2, 3, 420),
(312, NULL, 3, 2, 420),
(313, NULL, 5, 1, 420),
(314, NULL, 6, 1, 420),
(315, 243, 2, 2, NULL),
(316, 244, 2, 2, NULL),
(317, 245, 2, 2, NULL),
(318, 245, 3, 2, NULL),
(319, NULL, 2, 2, 421),
(320, NULL, 3, 2, 421),
(325, 246, 2, 2, NULL),
(326, 246, 3, 1, NULL),
(327, NULL, 2, 2, 422),
(328, NULL, 3, 1, 422),
(329, NULL, 2, 2, 423),
(330, NULL, 3, 1, 423),
(331, NULL, 2, 2, 424),
(332, NULL, 3, 1, 424),
(333, 247, 2, 1, NULL),
(334, NULL, 2, 1, 425),
(335, NULL, 2, 1, 426),
(336, NULL, 2, 2, 429),
(337, 250, 2, 2, NULL),
(338, NULL, 2, 2, 430);

-- --------------------------------------------------------

--
-- Structure de la table `devis_options`
--

DROP TABLE IF EXISTS `devis_options`;
CREATE TABLE IF NOT EXISTS `devis_options` (
  `devis_id` int NOT NULL,
  `options_id` int NOT NULL,
  PRIMARY KEY (`devis_id`,`options_id`),
  KEY `IDX_42DB61DB41DEFADA` (`devis_id`),
  KEY `IDX_42DB61DB3ADB05F1` (`options_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `devis_options`
--

INSERT INTO `devis_options` (`devis_id`, `options_id`) VALUES
(135, 2),
(136, 4),
(137, 2),
(138, 3),
(139, 4),
(140, 4),
(141, 2),
(142, 3),
(143, 2),
(144, 2),
(152, 3),
(152, 4),
(153, 2),
(153, 6),
(154, 3),
(155, 2),
(156, 3),
(158, 2),
(159, 2),
(161, 2),
(167, 6),
(168, 6),
(180, 4),
(181, 2),
(182, 2),
(183, 3),
(186, 2),
(186, 3),
(186, 4),
(186, 6),
(187, 2),
(187, 3),
(187, 4),
(187, 5),
(187, 6),
(188, 2),
(188, 3),
(188, 4),
(188, 5),
(188, 6),
(189, 2),
(191, 3),
(195, 4),
(195, 5),
(195, 6);

-- --------------------------------------------------------

--
-- Structure de la table `doctrine_migration_versions`
--

DROP TABLE IF EXISTS `doctrine_migration_versions`;
CREATE TABLE IF NOT EXISTS `doctrine_migration_versions` (
  `version` varchar(191) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `executed_at` datetime DEFAULT NULL,
  `execution_time` int DEFAULT NULL,
  PRIMARY KEY (`version`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;

--
-- Déchargement des données de la table `doctrine_migration_versions`
--

INSERT INTO `doctrine_migration_versions` (`version`, `executed_at`, `execution_time`) VALUES
('DoctrineMigrations\\Version20240118191014', '2024-02-04 19:54:53', 71),
('DoctrineMigrations\\Version20240912160822', '2024-09-14 18:44:05', 190),
('DoctrineMigrations\\Version20240915155537', '2024-12-15 10:51:22', 266),
('DoctrineMigrations\\Version20240915160858', '2024-09-23 18:29:08', 487),
('DoctrineMigrations\\Version20241215105100', '2024-12-15 10:51:22', 234),
('DoctrineMigrations\\Version20241222103345', '2024-12-22 10:33:55', 372),
('DoctrineMigrations\\Version20250721154332', '2025-07-21 15:44:14', 230);

-- --------------------------------------------------------

--
-- Structure de la table `etat_reservation`
--

DROP TABLE IF EXISTS `etat_reservation`;
CREATE TABLE IF NOT EXISTS `etat_reservation` (
  `id` int NOT NULL AUTO_INCREMENT,
  `libelle` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `etat_reservation`
--

INSERT INTO `etat_reservation` (`id`, `libelle`) VALUES
(1, 'EN ATTENTE'),
(2, 'EN COURS'),
(3, 'TERMINER');

-- --------------------------------------------------------

--
-- Structure de la table `faq`
--

DROP TABLE IF EXISTS `faq`;
CREATE TABLE IF NOT EXISTS `faq` (
  `id` int NOT NULL AUTO_INCREMENT,
  `question` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `reponse` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=41 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `faq`
--

INSERT INTO `faq` (`id`, `question`, `reponse`) VALUES
(2, 'Comment demander un devis ?', 'En remplissant notre formulaire de contact en ligne ou en nous contactant directement.'),
(3, 'Quelle est la durée d’une journée de location ?', 'La durée d’une journée de location est de 24h.'),
(4, 'Faites-vous de la location de longue durée ?', 'Pour toutes locations de plus de 3 mois, veuillez nous contacter.'),
(5, 'Quels sont les documents nécessaires pour louer ?', 'Il vous faudra votre permis de conduire ainsi que celui des autres conducteurs, carte d’identité ou passeport, et un justificatif de domicile de moins de 3 mois.'),
(6, 'Est-ce que je peux faire une réservation au nom d’une autre personne ?', 'Vous pouvez réserver au nom d’une autre personne, cependant tous les documents nécessaires à la réservation de la location doivent être à son nom.'),
(7, 'Est-ce que le prix de ma réservation peut changer ?', 'Les prix évoluent en permanence. Mais le prix de votre réservation évolue seulement si vous modifiez celle-ci.'),
(8, 'A partir de quel âge puis-je louer une voiture chez Joel Location ?', 'Vous pouvez louer à partir de 21 ans. Il vous faut un permis de conduire de 2 ans minimum valable, voir plus suivant la catégorie de voiture louée.'),
(9, 'Faut-il indiquer son numéro de vol lors de la réservation ?', 'Si nous devons vous livrer votre voiture de location à l’aéroport, vous devez nous communiquer votre numéro de vol. En cas de retard, nous serons informés. Votre voiture de location sera prête à votre arrivée.'),
(10, 'Je veux modifier ma réservation, puis-je le faire ?', 'C’est possible de modifier votre réservation à conditions d’avoir les disponibilités. Le prix de la nouvelle réservation pourrait être plus élevé que la réservation initiale.'),
(11, 'Puis-je ajouter des options supplémentaires ?', 'Vous pouvez ajouter des options supplémentaires et ce jusqu’au début de votre location.'),
(12, 'Puis-je modifier les options souscrites une fois la réservation validée ?', 'Les options validées et réglées ne pourront pas être remboursées mais un échange contre un autre est possible.'),
(13, 'Proposez-vous la location de sièges enfants ?', 'Oui, des sièges enfants sont possible en option.'),
(14, 'A quel moment vous informer si je souhaite annuler ma réservation ?', 'Vous devez nous informer le plus tôt possible, idéalement par téléphone suivi d’une confirmation par écrit par courriel.'),
(15, 'Comment annuler ma réservation ?', 'Pour annuler votre réservation, vous pouvez le faire en nous contactant. Une confirmation écrite sera nécessaire. Un justificatif pourrait vous êtes demandé dans certains cas.'),
(16, 'Quand régler le reste du solde de la location ?', 'Vous devez régler la totalité avant le début de la location. Si vous souhaitez nous payer par virement, ceci doit se faire au moins 5 jours ouvrés avant le début de location.'),
(17, 'Quels sont les moyens de paiement acceptés ?', 'Joel Location accepte les cartes Mastercard et Visa, les chèques, chèques vacances, espèces et virements. À la suite de plusieurs abus, nous encourageons vivement d’autres moyens de paiement mis à votre disposition que les chèques. Les chèques étrangers ne sont pas acceptés.'),
(18, 'Puis-je louer un véhicule sans mon permis ?', 'Non, il nous faudra au moins une photocopie de votre permis.'),
(19, 'J’arrive à l’aéroport de Pointe-à-Pitre. Où est ce que ma voiture sera livrée ?', 'Nous vous attendrons à la sortie bagages avec une pancarte à votre nom. La voiture se trouvera sur le parking de l’aéroport, juste en face.'),
(20, 'Est-ce je peux récupérer ou livrer le véhicule en dehors des heures d’ouverture ?', 'C’est possible en dehors des heures d’ouverture mais des frais supplémentaires peuvent être appliqués.'),
(21, 'Est-ce qu’il faut verser une caution ?', 'La caution est obligatoire. Elle permet de couvrir les frais en cas de dégradations sur le véhicule.'),
(22, 'Quel est le montant de la caution ?', 'La caution est à partir de 700€. Elle varie en fonction de la catégorie du véhicule.'),
(23, 'Je souhaiterais partir quelques jours à Marie-Galante durant mon séjour. Est-ce que je pourrais emmener mon véhicule ?', 'Si vous souhaitez conduire le véhicule hors de la Guadeloupe continentale, vous devrez solliciter notre accord préalable.'),
(24, 'Est-ce que la voiture de location peut être conduite par une autre personne ?', 'Un autre conducteur peut être autorisé à conduire à condition d’être déclaré comme conducteur supplémentaire sur le contrat de location.'),
(25, 'Est-ce que je peux fumer dans une voiture de location ?', 'Il est interdit de fumer dans les véhicules de location.'),
(26, 'Le véhicule a un problème mécanique, à qui dois-je m’adresser ?', 'Vous devez nous contacter immédiatement pour tout problème.'),
(27, 'Est-ce que je peux réparer moi-même mon véhicule de location ?', 'Toute réparation ou service quelconque effectuée sans l’accord de Joel Location est interdite et entrainera une perte totale de la caution.'),
(28, 'Où se trouve le constat d’accident ?', 'Il se trouve dans la boite à gants.'),
(29, 'Est-ce que la caution est débitée ?', 'Sans dommage ou d’éléments manquants sur le véhicule, la caution n’est pas débitée.'),
(30, 'Quand est-ce que ma caution est annulée ?', 'La caution est annulée à la fin du contrat, sans dommage sur le véhicule et sous réserve de vérification supplémentaire.'),
(31, 'Qui est responsable des infractions pendant la location ?', 'Vous êtes entièrement responsable de toutes infractions au code de la route, et aussi des amendes de stationnement et frais de parkings privés pendant votre location.'),
(32, 'Je remets le véhicule en retard, puis-je éviter une facturation supplémentaire ?', 'Vous disposez d’une (1) heure (« période de grâce ») une fois l’heure de restitution passée pour nous remettre le véhicule, sans qu’une journée supplémentaire vous soit facturée, sous pénalité après comptabilisée comme une journée supplémentaire.'),
(33, 'Puis-je bénéficier d’un délai de courtoisie lors de mon retour ?', 'Vous disposez d’un délai d’1 heure.'),
(34, 'Comment prolonger mon contrat de location ?', 'Vous pouvez prolonger votre contrat de location en nous informons 48 heures avant la fin de celle-ci, et sous condition de disponibilité.'),
(35, 'Quelle procédure respecter pour remettre la voiture ?', 'Vérifier l’état intérieur et extérieur du véhicule et signaler tout dommage. Faire le plein et nettoyer l’intérieur et l’extérieur.'),
(36, 'Pourquoi suis-je facturé un nettoyage spécial ?', 'Un nettoyage spécial demande du temps et des moyens supplémentaires pour remettre le véhicule en état pour une location.'),
(37, 'Dois-je rendre le véhicule avec un plein de carburant ?', 'Le véhicule est loué avec un plein et doit être rendu avec un plein.'),
(38, 'Pourquoi le montant de ma facture est plus élevé que lors de ma réservation ?', 'Votre facture peut être plus élevée que la réservation si vous avez pris des options supplémentaires après la réservation.'),
(39, 'Puis-je modifier l’adresse de facturation ?', 'Vous pouvez le faire en nous contactant directement par courriel et en nous précisant votre numéro de contrat/devis.'),
(40, 'Où sont les conditions générales de location ?', 'Vous pouvez consulter les conditions générales de location sur notre site internet, ou demander en avance qu’une copie vous soit fournie avec votre contrat de location.');

-- --------------------------------------------------------

--
-- Structure de la table `frais_suppl_resa`
--

DROP TABLE IF EXISTS `frais_suppl_resa`;
CREATE TABLE IF NOT EXISTS `frais_suppl_resa` (
  `id` int NOT NULL AUTO_INCREMENT,
  `reservation_id` int NOT NULL,
  `description` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `prix_unitaire` double NOT NULL,
  `quantite` double NOT NULL,
  `remise` double DEFAULT NULL,
  `total_ht` double NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_9B1D74A2B83297E7` (`reservation_id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `garantie`
--

DROP TABLE IF EXISTS `garantie`;
CREATE TABLE IF NOT EXISTS `garantie` (
  `id` int NOT NULL AUTO_INCREMENT,
  `appelation` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `prix` double NOT NULL,
  `description` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `garantie`
--

INSERT INTO `garantie` (`id`, `appelation`, `prix`, `description`) VALUES
(4, 'garantie bris de glace & pneus', 90, 'Ce produit élimine votre éventuelle responsabilité envers nous ; en cas de dommage causé aux vitres du véhicule (y compris au toit ouvrant) et aux pneus uniquement.'),
(5, 'assistance esprit tranquille', 100, 'Ce produit offre une couverture pour la récupération du véhicule et les frais de demande d’intervention en urgence dont vous devriez normalement vous acquitter, si la demande d’intervention ou le dépannage étaient imputables à une faute de votre part. Exemples de frais pour demande d’intervention en urgence couverts : clés perdus, batterie déchargée, erreur de carburant.'),
(6, 'assistance premium', 160, 'L’Assistance Premium est un produit optionnel qui complète l’assurance responsabilité civile incluse dans votre prix de location. Il offre une couverture pour la récupération du véhicule et les frais de demande d’intervention en urgence dont vous devriez normalement vous acquitter si la demande d’intervention ou le dépannage étaient imputables à une faute de votre part. Ce produit couvre tout dommage ou perte au véhicule à valeur total du véhicule.');

-- --------------------------------------------------------

--
-- Structure de la table `infos_resa`
--

DROP TABLE IF EXISTS `infos_resa`;
CREATE TABLE IF NOT EXISTS `infos_resa` (
  `id` int NOT NULL AUTO_INCREMENT,
  `nbr_adultes` int DEFAULT NULL,
  `nbr_enfants` int DEFAULT NULL,
  `nbr_bebes` int DEFAULT NULL,
  `infos_internes` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=24 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `infos_resa`
--

INSERT INTO `infos_resa` (`id`, `nbr_adultes`, `nbr_enfants`, `nbr_bebes`, `infos_internes`) VALUES
(2, NULL, NULL, NULL, NULL),
(3, NULL, NULL, NULL, NULL),
(4, 2, 1, 1, NULL),
(5, NULL, NULL, NULL, NULL),
(6, NULL, NULL, NULL, NULL),
(7, NULL, NULL, NULL, NULL),
(8, 4, 1, 1, NULL),
(9, NULL, NULL, NULL, NULL),
(10, NULL, NULL, NULL, NULL),
(11, NULL, NULL, NULL, NULL),
(12, NULL, NULL, NULL, NULL),
(13, NULL, NULL, NULL, NULL),
(14, NULL, NULL, NULL, NULL),
(15, NULL, NULL, NULL, NULL),
(16, NULL, NULL, NULL, NULL),
(17, NULL, NULL, NULL, NULL),
(18, NULL, NULL, NULL, NULL),
(19, NULL, NULL, NULL, NULL),
(20, NULL, NULL, NULL, NULL),
(21, NULL, NULL, NULL, NULL),
(22, NULL, NULL, NULL, NULL),
(23, NULL, NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Structure de la table `infos_vol_resa`
--

DROP TABLE IF EXISTS `infos_vol_resa`;
CREATE TABLE IF NOT EXISTS `infos_vol_resa` (
  `id` int NOT NULL AUTO_INCREMENT,
  `compagnie_aller` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `compagnie_retour` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `num_vol_aller` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `num_vol_retour` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `heure_vol_aller` datetime DEFAULT NULL,
  `heure_vol_retour` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=24 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `infos_vol_resa`
--

INSERT INTO `infos_vol_resa` (`id`, `compagnie_aller`, `compagnie_retour`, `num_vol_aller`, `num_vol_retour`, `heure_vol_aller`, `heure_vol_retour`) VALUES
(2, 'Air Antilles Express', 'Air Antilles Express', NULL, NULL, NULL, NULL),
(3, 'Air Antilles Express', 'Air Antilles Express', NULL, NULL, NULL, NULL),
(4, 'Air Antilles Express', 'Air Antilles Express', '777-200', '777-300', '2022-05-23 06:00:00', '2022-05-23 14:00:00'),
(5, 'Air France', 'Air France', 'AF0792', 'AF0763', '2022-07-18 18:35:00', '2022-07-31 20:55:00'),
(6, 'CorsAir', 'CorsAir', 'SS926', 'SS927', '2023-03-17 15:15:00', '2023-03-31 19:00:00'),
(7, 'Air Antilles Express', 'Air Antilles Express', NULL, NULL, NULL, NULL),
(8, 'Air Antilles Express', 'Air Antilles Express', 'AF0792', 'SS927', NULL, NULL),
(9, 'Air Canada', 'Air Canada', 'AC0948', 'AC0949', '2022-08-11 12:55:00', '2022-08-22 10:00:00'),
(10, 'Air Antilles Express', 'Air Antilles Express', NULL, NULL, NULL, NULL),
(11, 'Air Antilles Express', 'Air Antilles Express', NULL, NULL, NULL, NULL),
(12, 'Air Antilles Express', 'Air Antilles Express', NULL, NULL, NULL, NULL),
(13, 'Air Antilles Express', 'Air Antilles Express', NULL, NULL, NULL, NULL),
(14, 'Air Antilles Express', 'Air Antilles Express', NULL, NULL, NULL, NULL),
(15, 'Air Caraîbes', 'Air Caraîbes', 'TX542', 'TX543', '2024-01-19 18:30:00', '2024-02-13 20:45:00'),
(16, 'Air Antilles Express', 'Air Antilles Express', NULL, NULL, NULL, NULL),
(17, 'Air Antilles Express', 'Air Antilles Express', NULL, NULL, NULL, NULL),
(18, 'Air Antilles Express', 'Air Antilles Express', NULL, NULL, NULL, NULL),
(19, 'Air Antilles Express', 'Air Antilles Express', NULL, NULL, NULL, NULL),
(20, 'Air Antilles Express', 'Air Antilles Express', NULL, NULL, NULL, NULL),
(21, 'Air Antilles Express', 'Air Antilles Express', NULL, NULL, NULL, NULL),
(22, 'Air Antilles Express', 'Air Antilles Express', NULL, NULL, NULL, NULL),
(23, 'Air Antilles Express', 'Air Antilles Express', NULL, NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Structure de la table `mail`
--

DROP TABLE IF EXISTS `mail`;
CREATE TABLE IF NOT EXISTS `mail` (
  `id` int NOT NULL AUTO_INCREMENT,
  `nom` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `prenom` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `mail` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `objet` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `contenu` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `date_reception` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=351 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `mail`
--

INSERT INTO `mail` (`id`, `nom`, `prenom`, `mail`, `objet`, `contenu`, `date_reception`) VALUES
(38, 'Rija no anarako rakotoarinelina', NULL, '<EMAIL>', 'Lien de devis', 'success', '2024-10-23 18:25:21'),
(39, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', 'Lien de devis DV00001', 'failed', '2024-10-23 18:31:39'),
(40, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', 'Lien la reservation CPT2024OCT00384', 'failed', '2024-10-27 07:32:11'),
(41, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', 'Lien la reservation CPT2024OCT00384', 'failed', '2024-10-27 07:42:48'),
(42, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', 'Lien la reservation CPT2024OCT00384', 'failed', '2024-10-27 07:58:54'),
(43, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', 'Lien la reservation CPT2024OCT00384', 'failed', '2024-10-27 08:31:49'),
(44, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', 'Lien la reservation CPT2024OCT00384', 'failed', '2024-10-27 08:32:53'),
(45, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', 'Lien la reservation CPT2024OCT00384', 'failed', '2024-10-27 08:34:00'),
(46, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', 'Lien la reservation CPT2024OCT00384', 'failed', '2024-10-27 08:42:18'),
(47, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', 'Lien la reservation CPT2024OCT00384', 'success', '2024-10-27 08:50:05'),
(48, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', 'Lien la reservation CPT2024OCT00384', 'success', '2024-10-27 08:52:42'),
(49, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', 'Lien la reservation CPT2024OCT00384', 'success', '2024-10-27 08:54:45'),
(50, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', 'Lien la reservation CPT2024OCT00384', 'success', '2024-10-27 08:57:01'),
(51, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', 'Lien la reservation CPT2024OCT00384', 'success', '2024-10-27 09:00:07'),
(52, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', 'Lien la reservation CPT2024OCT00384', 'success', '2024-10-27 09:02:46'),
(53, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', 'Lien la reservation CPT2024OCT00384', 'success', '2024-10-27 09:06:26'),
(54, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', 'Lien la reservation CPT2024OCT00384', 'success', '2024-10-27 11:24:29'),
(55, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', 'Lien la reservation CPT2024OCT00384', 'success', '2024-10-27 11:25:48'),
(56, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', 'Lien la reservation CPT2024OCT00384', 'success', '2024-10-27 11:29:53'),
(57, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', 'Lien la reservation CPT2024OCT00384', 'success', '2024-10-27 11:44:59'),
(58, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', 'Lien la reservation CPT2024OCT00384', 'success', '2024-10-27 11:46:39'),
(59, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', 'Liendu devis DV2400180', 'success', '2024-10-27 17:58:15'),
(60, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', 'Liendu devis DV2400180', 'success', '2024-10-27 17:59:53'),
(61, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', 'Liendu devis DV2400180', 'success', '2024-10-27 18:05:11'),
(62, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', 'Lien la reservation CPT2024OCT00384', 'success', '2024-10-27 18:05:47'),
(63, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', 'Lien la reservation CPT2024OCT00384', 'success', '2024-10-27 18:06:37'),
(64, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', '  contrat CPT2024OCT00384', 'success', '2024-10-27 18:12:09'),
(65, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', '  contrat CPT2024OCT00384', 'success', '2024-10-27 18:16:31'),
(66, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', '  contrat CPT2024OCT00384', 'success', '2024-10-27 21:22:04'),
(67, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', '  contrat CPT2024OCT00384', 'failed', '2024-10-28 17:27:42'),
(68, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', 'facture CPT2024OCT00384', 'failed', '2024-10-28 17:29:58'),
(69, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', 'facture CPT2024OCT00384', 'failed', '2024-10-28 17:32:03'),
(70, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', 'facture CPT2024OCT00384', 'success', '2024-10-28 17:35:07'),
(71, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', 'facture CPT2024OCT00384', 'success', '2024-10-28 17:38:41'),
(72, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', 'facture CPT2024OCT00384', 'success', '2024-10-28 17:45:47'),
(73, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', 'contrat CPT2024OCT00384', 'success', '2024-10-28 17:46:16'),
(74, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', 'devis DV2400180', 'success', '2024-10-28 17:46:48'),
(75, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', 'devis DV2400180', 'success', '2024-10-28 17:58:14'),
(76, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', 'devis DV2400180', 'success', '2024-10-28 18:28:43'),
(77, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', 'devis DV2400180', 'success', '2024-10-28 18:32:36'),
(78, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', 'devis DV2400180', 'success', '2024-10-28 18:34:40'),
(79, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', 'devis DV2400180', 'success', '2024-10-28 18:38:43'),
(80, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', 'devis DV2400180', 'success', '2024-10-28 18:41:56'),
(81, 'rija', '', '<EMAIL>', 'contact', '{\"nom\":\"rija\",\"email\":\"<EMAIL>\",\"telephone\":\"0325072183\",\"adresse\":\"tana\",\"objet\":\"Demande de devis\",\"message\":\"ceci est un test bro\"}', '2024-10-28 20:05:32'),
(82, 'fdsdsf', '', '<EMAIL>', 'contact', '{\"nom\":\"fdsdsf\",\"email\":\"<EMAIL>\",\"telephone\":\"0325072183\",\"adresse\":\"testset\",\"objet\":\"Demande de devis\",\"message\":\"fdsfsdfsd\"}', '2024-10-28 20:06:38'),
(83, 'fdsfsd', '', '<EMAIL>', 'contact', '{\"nom\":\"fdsfsd\",\"email\":\"<EMAIL>\",\"telephone\":\"0325072183\",\"adresse\":\"rrrrr\",\"objet\":\"Demande de devis\",\"message\":\"fdsfsfsd\"}', '2024-10-28 20:18:11'),
(84, 'fjskdjfl', '', '<EMAIL>', 'contact', '{\"nom\":\"fjskdjfl\",\"email\":\"<EMAIL>\",\"telephone\":\"0325072183\",\"adresse\":\"test\",\"objet\":\"Demande de devis\",\"message\":\"test\"}', '2024-10-29 19:38:27'),
(85, 'roka', '', '<EMAIL>', 'contact', '{\"nom\":\"roka\",\"emailClient\":\"<EMAIL>\",\"telephone\":\"0325072183\",\"adresse\":\"rrrrr\",\"objet\":\"Demande de devis\",\"message\":\"test\"}', '2024-10-29 19:51:52'),
(86, 'rija', '', '<EMAIL>', 'contact', '{\"nom\":\"rija\",\"emailClient\":\"<EMAIL>\",\"telephone\":\"0325072183\",\"adresse\":\"rija rakoto\",\"objet\":\"Demande de devis\",\"message\":\"mety sa tsy mety\"}', '2024-10-29 20:02:50'),
(87, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', 'facture CPT2024OCT00384', 'success', '2024-10-29 20:22:13'),
(88, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', 'contrat CPT2024OCT00384', 'success', '2024-10-29 20:22:56'),
(89, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', 'facture CPT2024OCT00384', 'success', '2024-10-29 20:32:14'),
(90, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', 'devis DV2400180', 'success', '2024-10-29 20:33:00'),
(91, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', 'devis DV2400180', 'success', '2024-10-29 20:35:21'),
(92, 'rija', '', '<EMAIL>', 'contact', '{\"nom\":\"rija\",\"emailClient\":\"<EMAIL>\",\"telephone\":\"0325072183\",\"adresse\":\"rija rakoto\",\"objet\":\"Demande de devis\",\"message\":\"test\"}', '2024-10-29 20:39:20'),
(93, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', 'contrat CPT2024OCT00384', 'success', '2024-10-29 20:54:32'),
(94, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', 'facture CPT2024OCT00384', 'success', '2024-10-29 20:55:10'),
(95, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', 'contrat CPT2024OCT00384', 'success', '2024-10-29 20:57:03'),
(96, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', 'contrat CPT2024OCT00384', 'success', '2024-10-29 20:58:10'),
(97, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', 'facture CPT2024OCT00384', 'success', '2024-10-29 20:58:54'),
(98, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', 'devis DV2400180', 'success', '2024-10-29 20:59:27'),
(99, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', 'contrat CPT2024OCT00384', 'success', '2024-10-30 21:40:04'),
(100, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', 'facture CPT2024OCT00384', 'success', '2024-11-07 19:31:16'),
(101, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', 'devis DV2400180', 'success', '2024-11-07 19:52:36'),
(102, 'rakotoarinelina', 'Rija no anarako', ' <EMAIL>', 'facture CPT2024OCT00384', 'success', '2024-11-14 22:17:37'),
(103, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', 'devis DV2400183', 'success', '2024-11-18 17:20:16'),
(104, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', 'devis DV2400185', 'success', '2024-11-24 11:39:30'),
(105, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', 'devis DV2400186', 'success', '2024-11-27 17:50:53'),
(106, 'rakotoarinelina', 'Rija no anarako', '<EMAIL>', 'devis DV2400188', 'success', '2024-12-02 17:36:51'),
(107, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2400189', 'success', '2024-12-09 19:17:37'),
(108, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2400191', 'success', '2024-12-10 21:35:42'),
(109, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2400192', 'success', '2024-12-20 13:55:10'),
(110, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2400192', 'success', '2024-12-22 10:30:22'),
(111, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2400192', 'success', '2024-12-22 10:33:45'),
(112, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2400195', 'success', '2024-12-22 11:39:40'),
(113, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2400196', 'success', '2024-12-22 12:52:40'),
(114, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2400202', 'success', '2024-12-23 21:02:38'),
(115, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2500206', 'success', '2025-01-07 18:15:06'),
(116, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2500206', 'success', '2025-01-08 21:24:21'),
(117, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2500206', 'success', '2025-01-08 21:26:48'),
(118, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2400204', 'success', '2025-01-08 21:28:30'),
(119, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2400204', 'success', '2025-01-08 21:32:14'),
(120, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2400204', 'success', '2025-01-08 21:33:03'),
(121, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2400204', 'success', '2025-01-08 21:33:42'),
(122, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2400204', 'success', '2025-01-08 21:34:31'),
(123, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2400204', 'success', '2025-01-08 21:36:35'),
(124, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2400204', 'success', '2025-01-08 21:38:59'),
(125, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2400204', 'success', '2025-01-08 21:43:47'),
(126, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2400204', 'success', '2025-01-08 21:47:30'),
(127, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'facture CPTGP2021NOV00001', 'success', '2025-01-08 21:51:15'),
(128, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'contrat CPTGP2021NOV00001', 'success', '2025-01-08 21:52:25'),
(129, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'facture CPTGP2021NOV00001', 'success', '2025-01-08 21:53:10'),
(130, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2500206', 'success', '2025-01-09 20:56:20'),
(131, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2500206', 'success', '2025-01-09 20:57:06'),
(132, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2500206', 'success', '2025-01-09 20:58:56'),
(133, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2500206', 'success', '2025-01-09 21:05:37'),
(134, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2500206', 'success', '2025-01-09 21:08:30'),
(135, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2500206', 'success', '2025-01-09 21:09:35'),
(136, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2500206', 'failed', '2025-01-09 21:11:10'),
(137, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2500206', 'success', '2025-01-09 21:11:47'),
(138, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2500206', 'success', '2025-01-09 21:14:49'),
(139, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2500206', 'success', '2025-01-09 21:15:59'),
(140, 'rakoto', 'kapoka', '<EMAIL>', 'devis DV2500207', 'success', '2025-01-09 21:19:20'),
(141, 'rakoto', 'kapoka', '<EMAIL>', 'devis DV2500207', 'success', '2025-01-09 21:30:44'),
(142, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2500206', 'success', '2025-01-09 21:32:31'),
(143, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2500206', 'success', '2025-01-09 21:33:43'),
(144, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2500206', 'success', '2025-01-23 18:42:05'),
(145, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2500206', 'success', '2025-01-23 19:07:27'),
(146, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2500206', 'success', '2025-01-23 19:09:30'),
(147, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2500206', 'success', '2025-01-25 18:27:38'),
(148, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2500206', 'success', '2025-01-25 18:29:02'),
(149, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2500206', 'success', '2025-01-25 19:21:44'),
(150, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2500206', 'success', '2025-01-25 20:17:20'),
(151, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2500206', 'success', '2025-01-25 21:06:45'),
(152, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2500206', 'success', '2025-01-26 13:12:45'),
(153, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2500206', 'success', '2025-01-27 19:19:01'),
(154, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2500206', 'success', '2025-01-27 19:23:30'),
(155, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2500206', 'success', '2025-01-27 19:25:31'),
(156, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2500206', 'success', '2025-01-27 19:26:16'),
(157, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2500206', 'success', '2025-02-03 18:11:30'),
(158, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2500206', 'success', '2025-02-12 17:58:53'),
(159, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2500206', 'success', '2025-02-12 17:59:40'),
(160, 'rakoto', 'kapoka', '<EMAIL>', 'devis DV2500207', 'success', '2025-02-16 08:35:13'),
(161, 'rakoto', 'kapoka', '<EMAIL>', 'devis DV2500207', 'success', '2025-02-16 08:37:29'),
(162, 'rakoto', 'kapoka', '<EMAIL>', 'devis DV2500207', 'success', '2025-02-16 08:39:18'),
(163, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2500206', 'success', '2025-02-16 13:49:25'),
(164, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'contrat CPT2024DEC00399', 'success', '2025-02-16 13:51:30'),
(165, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'facture CPT2024DEC00399', 'success', '2025-02-16 13:51:46'),
(166, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'facture CPT2024DEC00399', 'success', '2025-02-16 13:52:41'),
(167, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'contrat CPT2024DEC00399', 'success', '2025-02-16 13:52:50'),
(168, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'facture CPT2024DEC00399', 'success', '2025-02-18 19:47:59'),
(169, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'contrat CPT2024DEC00399', 'success', '2025-02-18 19:48:22'),
(170, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'facture CPT2024DEC00399', 'success', '2025-02-18 19:59:47'),
(171, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'facture CPT2024DEC00399', 'success', '2025-02-18 20:01:02'),
(172, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'facture CPT2024DEC00399', 'success', '2025-02-18 20:02:17'),
(173, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'contrat CPT2024DEC00399', 'success', '2025-02-18 20:03:06'),
(174, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'contrat CPT2024DEC00399', 'success', '2025-02-18 20:03:29'),
(175, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'facture CPT2024DEC00399', 'success', '2025-02-18 20:03:51'),
(176, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'facture CPT2024DEC00399', 'success', '2025-02-18 20:04:47'),
(177, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'facture CPT2024DEC00399', 'success', '2025-02-18 20:07:39'),
(178, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'contrat CPT2024DEC00399', 'success', '2025-02-18 20:11:47'),
(179, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'facture CPT2024DEC00399', 'success', '2025-02-20 18:28:31'),
(180, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'facture CPT2024DEC00399', 'success', '2025-02-20 18:30:11'),
(181, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'contrat CPT2024DEC00399', 'success', '2025-02-20 18:43:08'),
(182, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'facture CPT2024DEC00399', 'success', '2025-02-20 18:49:08'),
(183, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'contrat CPT2024DEC00399', 'success', '2025-02-20 18:49:17'),
(184, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'facture CPT2024DEC00399', 'success', '2025-02-20 19:25:57'),
(185, 'rakoto', 'kapoka', '<EMAIL>', 'devis DV2500207', 'success', '2025-02-20 19:35:09'),
(186, 'rakoto', 'kapoka', '<EMAIL>', 'devis DV2500207', 'success', '2025-02-20 19:36:30'),
(187, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'facture CPT2024DEC00399', 'success', '2025-02-24 17:28:17'),
(188, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'facture CPT2024DEC00399', 'failed', '2025-02-24 17:58:10'),
(189, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'facture CPT2024DEC00399', 'success', '2025-02-24 18:04:53'),
(190, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'facture CPT2024DEC00399', 'success', '2025-02-24 18:26:54'),
(191, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'facture CPT2024DEC00399', 'failed', '2025-02-24 18:28:56'),
(192, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'contrat CPT2024DEC00399', 'failed', '2025-02-24 18:30:12'),
(193, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'facture CPT2024DEC00399', 'failed', '2025-02-24 19:29:15'),
(194, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'facture CPT2024DEC00399', 'success', '2025-02-24 19:39:55'),
(195, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'facture CPT2024DEC00399', 'success', '2025-02-24 19:41:24'),
(196, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'facture CPT2024DEC00399', 'success', '2025-02-24 19:42:50'),
(197, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'facture CPT2024DEC00399', 'success', '2025-02-24 19:44:09'),
(198, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'facture CPT2024DEC00399', 'success', '2025-02-24 19:44:52'),
(199, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'facture CPT2024DEC00399', 'failed', '2025-02-24 19:45:17'),
(200, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'contrat CPT2024DEC00399', 'failed', '2025-02-24 19:45:21'),
(201, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'facture CPT2024DEC00399', 'success', '2025-02-24 19:46:13'),
(202, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'facture CPT2024DEC00399', 'failed', '2025-02-24 19:52:32'),
(203, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'facture CPT2024DEC00399', 'failed', '2025-02-24 19:52:40'),
(204, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'contrat CPT2024DEC00399', 'failed', '2025-02-24 19:53:14'),
(205, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'facture CPT2024DEC00399', 'success', '2025-02-24 20:09:02'),
(206, 'rakoto', 'kapoka', '<EMAIL>', 'devis DV2500207', 'success', '2025-02-25 21:33:49'),
(207, 'rakoto', 'kapoka', '<EMAIL>', 'devis DV2500207', 'success', '2025-02-25 21:37:30'),
(208, 'rakoto', 'kapoka', '<EMAIL>', 'devis DV2500207', 'success', '2025-02-25 21:38:00'),
(209, 'rakoto', 'kapoka', '<EMAIL>', 'devis DV2500207', 'success', '2025-02-25 21:40:24'),
(210, 'rakoto', 'kapoka', '<EMAIL>', 'devis DV2500207', 'success', '2025-02-25 21:56:17'),
(211, 'rakoto', 'kapoka', '<EMAIL>', 'devis DV2500207', 'success', '2025-02-25 22:00:20'),
(212, 'rakoto', 'kapoka', '<EMAIL>', 'devis DV2500207', 'success', '2025-02-25 22:02:04'),
(213, 'rakoto', 'kapoka', '<EMAIL>', 'devis DV2500207', 'success', '2025-02-25 22:15:24'),
(214, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'facture CPT2024DEC00399', 'failed', '2025-03-02 08:55:12'),
(215, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'contrat CPT2024DEC00399', 'failed', '2025-03-02 08:57:29'),
(216, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'contrat CPT2024DEC00399', 'failed', '2025-03-02 08:58:30'),
(217, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'facture CPT2024DEC00399', 'success', '2025-03-02 08:59:28'),
(218, 'rakoto', 'kapoka', '<EMAIL>', 'devis DV2500207', 'success', '2025-03-02 09:15:56'),
(219, 'rakoto', 'kapoka', '<EMAIL>', 'devis DV2500207', 'success', '2025-03-02 09:50:04'),
(220, 'rakoto', 'kapoka', '<EMAIL>', 'devis DV2500207', 'success', '2025-03-02 09:52:07'),
(221, 'rakoto', 'kapoka', '<EMAIL>', 'devis DV2500207', 'success', '2025-03-02 12:25:27'),
(222, 'rakoto', 'kapoka', '<EMAIL>', 'devis DV2500207', 'success', '2025-03-02 12:27:11'),
(223, 'rakoto', 'yahoo', '<EMAIL>', 'devis DV2500208', 'success', '2025-03-05 17:52:55'),
(224, 'rakoto', 'yahoo', '<EMAIL>', 'devis DV2500208', 'success', '2025-03-05 18:08:58'),
(225, 'rakoto', 'yahoo', '<EMAIL>', 'devis DV2500208', 'failed', '2025-03-13 21:20:57'),
(226, 'rakoto', 'yahoo', '<EMAIL>', 'devis DV2500208', 'failed', '2025-03-13 21:25:08'),
(227, 'rakoto', 'yahoo', '<EMAIL>', 'devis DV2500208', 'failed', '2025-03-13 21:29:09'),
(228, 'rakoto', 'yahoo', '<EMAIL>', 'devis DV2500208', 'failed', '2025-03-13 21:40:28'),
(229, 'rakoto', 'yahoo', '<EMAIL>', 'devis DV2500208', 'success', '2025-03-13 21:46:02'),
(230, 'rakoto', 'yahoo', '<EMAIL>', 'devis DV2500208', 'failed', '2025-03-13 21:47:40'),
(231, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'contrat CPT2024DEC00399', 'success', '2025-03-15 20:07:27'),
(232, 'rakoto', 'yahoo', '<EMAIL>', 'devis DV2500208', 'success', '2025-03-16 13:24:48'),
(233, 'rakoto', 'yahoo', '<EMAIL>', 'devis DV2500208', 'success', '2025-03-16 13:40:44'),
(234, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'contrat CPT2024DEC00399', 'success', '2025-03-17 21:18:09'),
(235, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2500209', 'success', '2025-03-17 21:46:10'),
(236, 'rakoto', 'yahoo', '<EMAIL>', 'devis DV2500208', 'success', '2025-03-18 18:22:16'),
(237, 'rakoto', 'yahoo', '<EMAIL>', 'devis DV2500208', 'success', '2025-03-18 18:32:07'),
(238, 'rakoto', 'yahoo', '<EMAIL>', 'devis DV2500208', 'success', '2025-03-18 18:33:37'),
(239, 'rakoto', 'yahoo', '<EMAIL>', 'devis DV2500208', 'success', '2025-03-18 18:34:44'),
(240, 'rakoto', 'yahoo', '<EMAIL>', 'devis DV2500208', 'success', '2025-03-18 18:59:28'),
(241, 'rakoto', 'yahoo', '<EMAIL>', 'devis DV2500208', 'failed', '2025-03-18 19:03:25'),
(242, 'rakoto', 'yahoo', '<EMAIL>', 'devis DV2500208', 'success', '2025-03-18 19:09:19'),
(243, 'rakoto', 'yahoo', '<EMAIL>', 'devis DV2500208', 'success', '2025-03-18 19:11:54'),
(244, 'rakoto', 'yahoo', '<EMAIL>', 'devis DV2500208', 'success', '2025-03-18 19:17:59'),
(245, 'rakoto', 'yahoo', '<EMAIL>', 'devis DV2500208', 'success', '2025-03-18 19:19:42'),
(246, 'rakoto', 'yahoo', '<EMAIL>', 'devis DV2500208', 'success', '2025-03-18 19:22:02'),
(247, 'rakoto', 'yahoo', '<EMAIL>', 'devis DV2500208', 'success', '2025-03-18 19:22:34'),
(248, 'rakoto', 'yahoo', '<EMAIL>', 'devis DV2500208', 'success', '2025-03-18 19:27:08'),
(249, 'rakoto', 'yahoo', '<EMAIL>', 'devis DV2500208', 'success', '2025-03-18 19:33:03'),
(250, 'rakoto', 'yahoo', '<EMAIL>', 'devis DV2500208', 'success', '2025-03-18 19:36:13'),
(251, 'rakoto', 'yahoo', '<EMAIL>', 'devis DV2500208', 'success', '2025-03-18 19:53:35'),
(252, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2500210', 'success', '2025-03-20 17:37:46'),
(253, 'rakoto', 'yahoo', '<EMAIL>', 'devis DV2500208', 'success', '2025-03-25 19:19:32'),
(254, 'rakoto', 'yahoo', '<EMAIL>', 'devis DV2500208', 'success', '2025-03-25 19:27:14'),
(255, 'rakoto', 'yahoo', '<EMAIL>', 'devis DV2500208', 'success', '2025-03-25 19:32:42'),
(256, 'rakoto', 'yahoo', '<EMAIL>', 'devis DV2500208', 'success', '2025-03-25 19:40:14'),
(257, 'rakoto', 'yahoo', '<EMAIL>', 'devis DV2500208', 'success', '2025-03-25 20:12:26'),
(258, 'rakoto', 'yahoo', '<EMAIL>', 'devis DV2500208', 'success', '2025-03-25 20:13:06'),
(259, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2500211', 'success', '2025-03-31 12:13:47'),
(260, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2500212', 'success', '2025-04-03 19:24:06'),
(261, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2500213', 'success', '2025-04-08 17:50:53'),
(262, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2500214', 'success', '2025-04-10 18:15:45'),
(263, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2500215', 'success', '2025-04-15 19:51:35'),
(264, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2500216', 'success', '2025-04-17 17:45:47'),
(265, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2500217', 'success', '2025-04-17 17:57:58'),
(266, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis *********', 'success', '2025-04-20 14:45:22'),
(267, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis *********', 'success', '2025-04-20 16:21:46'),
(268, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis *********', 'success', '2025-04-20 16:40:46'),
(269, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis *********', 'success', '2025-04-21 18:56:28'),
(270, 'rija', 'benjamina', '<EMAIL>', 'devis *********', 'success', '2025-04-24 16:53:20'),
(271, 'rkdlsjflsj', '', '<EMAIL>', 'validation_inscription', '{\"nom\":\"rkdlsjflsj\",\"emailClient\":\"<EMAIL>\"}', '2025-04-29 21:40:39'),
(272, 'rkdlsjflsj', '', '<EMAIL>', 'validation_inscription', '{\"nom\":\"rkdlsjflsj\",\"emailClient\":\"<EMAIL>\"}', '2025-04-29 21:52:13'),
(273, 'rkdlsjflsj', '', '<EMAIL>', 'validation_inscription', '{\"nom\":\"rkdlsjflsj\",\"emailClient\":\"<EMAIL>\"}', '2025-04-29 21:59:24'),
(274, 'rkdlsjflsj', '', '<EMAIL>', 'validation_inscription', '{\"nom\":\"rkdlsjflsj\",\"emailClient\":\"<EMAIL>\"}', '2025-04-29 22:05:12'),
(275, 'rkdlsjflsj', '', '<EMAIL>', 'validation_inscription', '{\"nom\":\"rkdlsjflsj\",\"emailClient\":\"<EMAIL>\"}', '2025-05-01 08:24:44'),
(276, 'fsdfsdf', '', '<EMAIL>', 'validation_inscription', '{\"nom\":\"fsdfsdf\",\"emailClient\":\"<EMAIL>\"}', '2025-05-01 09:30:31'),
(277, 'fsdfsdf', '', '<EMAIL>', 'validation_inscription', '{\"nom\":\"fsdfsdf\",\"emailClient\":\"<EMAIL>\"}', '2025-05-01 09:41:31'),
(278, 'vxdfsdfs', '', '<EMAIL>', 'validation_inscription', '{\"nom\":\"vxdfsdfs\",\"emailClient\":\"<EMAIL>\"}', '2025-05-01 12:13:30'),
(279, 'vxdfsdfs', '', '<EMAIL>', 'validation_inscription', '{\"nom\":\"vxdfsdfs\",\"emailClient\":\"<EMAIL>\"}', '2025-05-01 12:18:55'),
(280, 'vxdfsdfs', '', '<EMAIL>', 'validation_inscription', '{\"nom\":\"vxdfsdfs\",\"emailClient\":\"<EMAIL>\"}', '2025-05-01 12:25:15'),
(281, 'vxdfsdfs', '', '<EMAIL>', 'validation_inscription', '{\"nom\":\"vxdfsdfs\",\"emailClient\":\"<EMAIL>\"}', '2025-05-01 12:25:15'),
(282, 'vxdfsdfs', '', '<EMAIL>', 'validation_inscription', '{\"nom\":\"vxdfsdfs\",\"emailClient\":\"<EMAIL>\"}', '2025-05-01 12:25:18'),
(283, 'vxdfsdfs', '', '<EMAIL>', 'validation_inscription', '{\"nom\":\"vxdfsdfs\",\"emailClient\":\"<EMAIL>\"}', '2025-05-01 12:25:18'),
(284, 'fsdfsdf', '', '<EMAIL>', 'validation_inscription', '{\"nom\":\"fsdfsdf\",\"emailClient\":\"<EMAIL>\"}', '2025-05-01 12:57:17'),
(285, 'fsdfsdf', '', '<EMAIL>', 'validation_inscription', '{\"nom\":\"fsdfsdf\",\"emailClient\":\"<EMAIL>\"}', '2025-05-01 12:57:18'),
(286, 'fsdfsdf', '', '<EMAIL>', 'validation_inscription', '{\"nom\":\"fsdfsdf\",\"emailClient\":\"<EMAIL>\"}', '2025-05-01 12:57:20'),
(287, 'fsdfsdf', '', '<EMAIL>', 'validation_inscription', '{\"nom\":\"fsdfsdf\",\"emailClient\":\"<EMAIL>\"}', '2025-05-01 12:57:20'),
(288, 'gdfgdg', '', '<EMAIL>', 'validation_inscription', '{\"nom\":\"gdfgdg\",\"emailClient\":\"<EMAIL>\"}', '2025-05-06 20:29:03'),
(289, 'gdfgdg', '', '<EMAIL>', 'validation_inscription', '{\"nom\":\"gdfgdg\",\"emailClient\":\"<EMAIL>\"}', '2025-05-06 20:29:03'),
(290, 'gdfgdg', '', '<EMAIL>', 'validation_inscription', '{\"nom\":\"gdfgdg\",\"emailClient\":\"<EMAIL>\"}', '2025-05-06 20:29:05'),
(291, 'gdfgdg', '', '<EMAIL>', 'validation_inscription', '{\"nom\":\"gdfgdg\",\"emailClient\":\"<EMAIL>\"}', '2025-05-06 20:29:05'),
(292, 'gdfgdg', '', '<EMAIL>', 'validation_inscription', '{\"nom\":\"gdfgdg\",\"emailClient\":\"<EMAIL>\"}', '2025-05-06 20:36:14'),
(293, 'gdfgdg', '', '<EMAIL>', 'validation_inscription', '{\"nom\":\"gdfgdg\",\"emailClient\":\"<EMAIL>\"}', '2025-05-06 20:36:14'),
(294, 'gdfgdg', '', '<EMAIL>', 'validation_inscription', '{\"nom\":\"gdfgdg\",\"emailClient\":\"<EMAIL>\"}', '2025-05-06 20:36:16'),
(295, 'gdfgdg', '', '<EMAIL>', 'validation_inscription', '{\"nom\":\"gdfgdg\",\"emailClient\":\"<EMAIL>\"}', '2025-05-06 20:36:16'),
(296, 'gdfgdg', '', '<EMAIL>', 'validation_inscription', '{\"nom\":\"gdfgdg\",\"emailClient\":\"<EMAIL>\"}', '2025-05-06 21:09:28'),
(297, 'gdfgdg', '', '<EMAIL>', 'validation_inscription', '{\"nom\":\"gdfgdg\",\"emailClient\":\"<EMAIL>\"}', '2025-05-06 21:09:28'),
(298, 'gdfgdg', '', '<EMAIL>', 'validation_inscription', '{\"nom\":\"gdfgdg\",\"emailClient\":\"<EMAIL>\"}', '2025-05-06 21:09:30'),
(299, 'gdfgdg', '', '<EMAIL>', 'validation_inscription', '{\"nom\":\"gdfgdg\",\"emailClient\":\"<EMAIL>\"}', '2025-05-06 21:09:30'),
(300, 'Dupont', '', '<EMAIL>', 'validation_inscription', '{\"nom\":\"Dupont\",\"emailClient\":\"<EMAIL>\"}', '2025-05-09 09:49:15'),
(301, 'Dupont', '', '<EMAIL>', 'validation_inscription', '{\"nom\":\"Dupont\",\"emailClient\":\"<EMAIL>\"}', '2025-05-09 09:49:16'),
(302, 'Dupont', '', '<EMAIL>', 'validation_inscription', '{\"nom\":\"Dupont\",\"emailClient\":\"<EMAIL>\"}', '2025-05-09 09:49:18'),
(303, 'Dupont', '', '<EMAIL>', 'validation_inscription', '{\"nom\":\"Dupont\",\"emailClient\":\"<EMAIL>\"}', '2025-05-09 09:49:18'),
(304, 'Dupont', '', '<EMAIL>', 'validation_inscription', '{\"nom\":\"Dupont\",\"emailClient\":\"<EMAIL>\"}', '2025-05-09 10:01:01'),
(305, 'Dupont', '', '<EMAIL>', 'validation_inscription', '{\"nom\":\"Dupont\",\"emailClient\":\"<EMAIL>\"}', '2025-05-09 10:01:01'),
(306, 'Dupont', '', '<EMAIL>', 'validation_inscription', '{\"nom\":\"Dupont\",\"emailClient\":\"<EMAIL>\"}', '2025-05-09 10:01:03'),
(307, 'Dupont', '', '<EMAIL>', 'validation_inscription', '{\"nom\":\"Dupont\",\"emailClient\":\"<EMAIL>\"}', '2025-05-09 10:01:03'),
(308, 'Dupont', '', '<EMAIL>', 'validation_inscription', '{\"nom\":\"Dupont\",\"emailClient\":\"<EMAIL>\"}', '2025-05-09 10:17:28'),
(309, 'Dupont', '', '<EMAIL>', 'validation_inscription', '{\"nom\":\"Dupont\",\"emailClient\":\"<EMAIL>\"}', '2025-05-09 10:17:28'),
(310, 'Dupont', '', '<EMAIL>', 'validation_inscription', '{\"nom\":\"Dupont\",\"emailClient\":\"<EMAIL>\"}', '2025-05-09 10:17:30'),
(311, 'Dupont', '', '<EMAIL>', 'validation_inscription', '{\"nom\":\"Dupont\",\"emailClient\":\"<EMAIL>\"}', '2025-05-09 10:17:30'),
(312, 'Dupont', '', '<EMAIL>', 'validation_inscription', '{\"nom\":\"Dupont\",\"emailClient\":\"<EMAIL>\"}', '2025-05-09 10:38:29'),
(313, 'Dupont', '', '<EMAIL>', 'validation_inscription', '{\"nom\":\"Dupont\",\"emailClient\":\"<EMAIL>\"}', '2025-05-09 10:38:29'),
(314, 'Dupont', '', '<EMAIL>', 'validation_inscription', '{\"nom\":\"Dupont\",\"emailClient\":\"<EMAIL>\"}', '2025-05-09 10:38:37'),
(315, 'Dupont', '', '<EMAIL>', 'validation_inscription', '{\"nom\":\"Dupont\",\"emailClient\":\"<EMAIL>\"}', '2025-05-09 10:38:37'),
(316, 'Dupont', '', '<EMAIL>', 'validation_inscription', '{\"nom\":\"Dupont\",\"emailClient\":\"<EMAIL>\"}', '2025-05-09 12:43:20'),
(317, 'Dupont', '', '<EMAIL>', 'validation_inscription', '{\"nom\":\"Dupont\",\"emailClient\":\"<EMAIL>\"}', '2025-05-09 12:43:20'),
(318, 'Dupont', '', '<EMAIL>', 'validation_inscription', '{\"nom\":\"Dupont\",\"emailClient\":\"<EMAIL>\"}', '2025-05-09 12:43:22'),
(319, 'Dupont', '', '<EMAIL>', 'validation_inscription', '{\"nom\":\"Dupont\",\"emailClient\":\"<EMAIL>\"}', '2025-05-09 12:43:22'),
(320, 'Dupont', 'Jean', '<EMAIL>', 'devis DV2500232', 'success', '2025-05-09 13:01:02'),
(321, 'Dupont', 'Jean', '<EMAIL>', 'devis DV2500233', 'success', '2025-05-12 19:21:46'),
(322, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2500234', 'success', '2025-05-20 18:15:47'),
(323, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'facture CPT2025MAI00419', 'success', '2025-05-20 18:18:20'),
(324, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2500235', 'success', '2025-05-26 17:41:37'),
(325, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2500236', 'success', '2025-06-05 18:36:55'),
(326, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2500239', 'success', '2025-06-24 18:19:46'),
(327, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2500240', 'success', '2025-06-24 18:38:55'),
(328, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2500241', 'success', '2025-06-26 13:14:13'),
(329, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2500241', 'success', '2025-06-26 13:14:36'),
(330, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2500242', 'success', '2025-06-26 13:22:25'),
(331, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2500243', 'success', '2025-06-26 13:41:22'),
(332, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2500243', 'success', '2025-06-26 13:53:13'),
(333, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2500244', 'success', '2025-07-03 20:33:44'),
(334, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'facture CPT2025JUI00420', 'success', '2025-07-03 20:35:31'),
(335, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'facture CPT2025JUI00420', 'success', '2025-07-03 20:40:10'),
(336, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV00001', 'success', '2025-07-10 18:54:05'),
(337, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'devis DV2500246', 'success', '2025-07-10 19:18:04'),
(338, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'paiement CPT2025JUIL00427', 'success', '2025-07-10 21:12:32'),
(339, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'paiement CPT2025JUIL00428', 'success', '2025-07-10 21:21:38'),
(340, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'appel_paiement CPT2025JUIL00429', 'success', '2025-07-16 07:10:23'),
(341, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'appel_paiement CPT2025JUIL00429', 'success', '2025-07-17 18:02:26'),
(342, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'appel_paiement CPT2025JUIL00429', 'success', '2025-07-17 18:05:55'),
(343, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'appel_paiement CPT2025JUIL00429', 'success', '2025-07-17 20:04:32'),
(344, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'appel_paiement CPT2025JUIL00430', 'success', '2025-07-17 20:18:32'),
(345, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'appel_paiement CPT2025JUIL00430', 'success', '2025-07-21 18:50:28'),
(346, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'appel_paiement CPT2025JUIL00430', 'success', '2025-07-21 18:50:48'),
(347, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'appel_paiement CPT2025JUIL00430', 'success', '2025-07-21 18:53:33'),
(348, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'appel_paiement CPTGP2021NOV00021', 'success', '2025-07-21 19:27:55'),
(349, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'appel_paiement CPTGP2021NOV00001', 'success', '2025-07-21 19:28:35'),
(350, 'rakotoarinelina', 'Rija no', '<EMAIL>', 'appel_paiement CPTGP2021NOV00021', 'success', '2025-07-21 19:30:08');

-- --------------------------------------------------------

--
-- Structure de la table `marque`
--

DROP TABLE IF EXISTS `marque`;
CREATE TABLE IF NOT EXISTS `marque` (
  `id` int NOT NULL AUTO_INCREMENT,
  `libelle` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `marque`
--

INSERT INTO `marque` (`id`, `libelle`) VALUES
(1, 'RENAULT'),
(2, 'CITROEN'),
(3, 'BMW');

-- --------------------------------------------------------

--
-- Structure de la table `message`
--

DROP TABLE IF EXISTS `message`;
CREATE TABLE IF NOT EXISTS `message` (
  `id` int NOT NULL AUTO_INCREMENT,
  `client_id` int NOT NULL,
  `message` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_B6BD307F19EB6921` (`client_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `modele`
--

DROP TABLE IF EXISTS `modele`;
CREATE TABLE IF NOT EXISTS `modele` (
  `id` int NOT NULL AUTO_INCREMENT,
  `marque_id` int DEFAULT NULL,
  `libelle` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_100285584827B9B2` (`marque_id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `modele`
--

INSERT INTO `modele` (`id`, `marque_id`, `libelle`) VALUES
(1, 1, 'Clio'),
(2, 1, 'Captur'),
(3, 1, 'Twingo'),
(4, 1, 'Logan'),
(5, 2, 'c12'),
(6, 2, 'C3'),
(7, 2, '2CV'),
(8, 3, 'Z3'),
(9, 2, 'C15 Diesel'),
(10, 1, 'Clio 5');

-- --------------------------------------------------------

--
-- Structure de la table `mode_paiement`
--

DROP TABLE IF EXISTS `mode_paiement`;
CREATE TABLE IF NOT EXISTS `mode_paiement` (
  `id` int NOT NULL AUTO_INCREMENT,
  `libelle` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `mode_paiement`
--

INSERT INTO `mode_paiement` (`id`, `libelle`) VALUES
(1, 'Espèces'),
(2, 'Carte Bleue'),
(3, 'Virement'),
(4, 'Chèque vacances'),
(5, 'Mandat SEPA'),
(6, 'Avoir');

-- --------------------------------------------------------

--
-- Structure de la table `mode_reservation`
--

DROP TABLE IF EXISTS `mode_reservation`;
CREATE TABLE IF NOT EXISTS `mode_reservation` (
  `id` int NOT NULL AUTO_INCREMENT,
  `libelle` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `mode_reservation`
--

INSERT INTO `mode_reservation` (`id`, `libelle`) VALUES
(2, 'CPT'),
(3, 'WEB');

-- --------------------------------------------------------

--
-- Structure de la table `options`
--

DROP TABLE IF EXISTS `options`;
CREATE TABLE IF NOT EXISTS `options` (
  `id` int NOT NULL AUTO_INCREMENT,
  `appelation` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `prix` double NOT NULL,
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `options`
--

INSERT INTO `options` (`id`, `appelation`, `prix`, `type`, `description`) VALUES
(2, 'Siège bébé', 30, 'siège', NULL),
(3, 'Siège nourrisson', 30, 'siège', NULL),
(4, 'Rehausseur : gratuit', 0, 'siège', NULL),
(5, 'Refueling', 80, 'refueling', 'Restitution du véhicule avec un réservoir inférieur au plein. Une bonne option si vous n\'avez pas le temps de faire le plein de carburant.'),
(6, 'Lavage', 35, 'lavage', 'Une option qui vous épargne du lavage et nettoyage du véhicule.Attention la caution sera remise après le lavage du véhicule.');

-- --------------------------------------------------------

--
-- Structure de la table `paiement`
--

DROP TABLE IF EXISTS `paiement`;
CREATE TABLE IF NOT EXISTS `paiement` (
  `id` int NOT NULL AUTO_INCREMENT,
  `reservation_id` int NOT NULL,
  `mode_paiement_id` int NOT NULL,
  `client_id` int NOT NULL,
  `montant` double NOT NULL,
  `date_paiement` datetime NOT NULL,
  `motif` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `stripe_session_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_B1DC7A1EB83297E7` (`reservation_id`),
  KEY `IDX_B1DC7A1E438F5B63` (`mode_paiement_id`),
  KEY `IDX_B1DC7A1E19EB6921` (`client_id`)
) ENGINE=InnoDB AUTO_INCREMENT=80 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `paiement`
--

INSERT INTO `paiement` (`id`, `reservation_id`, `mode_paiement_id`, `client_id`, `montant`, `date_paiement`, `motif`, `stripe_session_id`, `created_at`) VALUES
(30, 23, 2, 117, 200, '2021-11-21 13:25:27', 'Ajout paiement', NULL, '0000-00-00 00:00:00'),
(31, 22, 2, 117, 230, '2021-11-21 14:52:41', 'Réservation', NULL, '0000-00-00 00:00:00'),
(32, 22, 2, 117, 20, '2021-11-21 15:16:30', 'Réservation', NULL, '0000-00-00 00:00:00'),
(38, 25, 5, 146, 100, '2022-02-17 10:00:32', 'Réservation', NULL, '2022-02-17 10:00:32'),
(40, 31, 3, 148, 71, '2022-02-20 22:30:47', 'Réservation', NULL, '2022-02-20 22:30:47'),
(41, 63, 3, 147, 1, '2022-05-21 15:30:53', 'Réservation', 'cs_live_a12In4ymXg0M0k3DFkZB8Mbj4hYByc6EEUVabs57BwV3KnjtBYxeMZidq5', '2022-05-21 15:30:53'),
(42, 73, 3, 170, 270, '2022-06-10 06:25:48', 'Réservation', NULL, '2022-06-10 06:25:48'),
(43, 72, 3, 169, 170, '2022-06-10 06:32:55', 'Réservation', NULL, '2022-06-10 06:32:55'),
(44, 48, 3, 159, 1015, '2022-06-10 06:37:55', 'Réservation', NULL, '2022-06-10 06:37:55'),
(45, 49, 3, 160, 300, '2022-06-10 06:57:30', 'Réservation', NULL, '2022-06-10 06:57:30'),
(47, 77, 3, 172, 152, '2022-06-24 15:03:03', 'Réservation', NULL, '2022-06-24 15:03:03'),
(48, 95, 3, 179, 126, '2022-08-03 02:51:28', 'Réservation', NULL, '2022-08-03 02:51:28'),
(49, 97, 2, 181, 342.5, '2022-08-12 03:51:50', 'Réservation', NULL, '2022-08-12 03:51:50'),
(50, 251, 3, 248, 210, '2024-01-19 01:56:22', 'Réservation', NULL, '2024-01-19 01:56:22'),
(51, 373, 2, 296, 75, '2024-09-26 00:00:00', 'Réservation', NULL, '2024-09-26 23:53:40'),
(52, 375, 1, 297, 60, '2024-09-27 00:00:00', 'Réservation', NULL, '2024-09-27 20:13:36'),
(53, 376, 2, 298, 30, '2024-09-28 00:00:00', 'Réservation', NULL, '2024-09-28 15:27:20'),
(54, 405, 3, 117, 570, '2025-04-15 17:51:17', 'Réservation PayPal', NULL, '2025-04-15 17:51:17'),
(55, 407, 3, 117, 570, '2025-04-15 17:54:37', 'Réservation PayPal', NULL, '2025-04-15 17:54:37'),
(56, 408, 3, 117, 190, '2025-04-17 14:46:53', 'Réservation', '9W1035544K694903B', '2025-04-17 14:46:53'),
(57, 409, 3, 117, 540, '2025-04-17 14:58:36', 'Réservation', '1DF669967K111551W', '2025-04-17 14:58:36'),
(58, 410, 3, 117, 540, '2025-04-17 14:59:46', 'Réservation', '2CM2527188868773T', '2025-04-17 14:59:46'),
(59, 411, 3, 117, 540, '2025-04-17 15:00:57', 'Réservation', '4NM02837NV2159426', '2025-04-17 15:00:57'),
(60, 412, 3, 117, 540, '2025-04-17 15:05:58', 'Réservation', '62T55226PT2367946', '2025-04-17 15:05:58'),
(61, 413, 3, 117, 450, '2025-04-20 11:47:25', 'Réservation', '2PH10266EL759854J', '2025-04-20 11:47:25'),
(62, 414, 3, 117, 450, '2025-04-20 13:23:53', 'Réservation', '37U08168BC787872J', '2025-04-20 13:23:53'),
(63, 417, 3, 373, 925, '2025-05-09 11:28:14', 'Réservation', '52606439UX590650F', '2025-05-09 11:28:14'),
(64, 418, 3, 375, 100, '2025-05-12 16:26:58', 'Réservation', '8SN64339PD1649737', '2025-05-12 16:26:58'),
(65, 419, 3, 117, 720, '2025-05-20 15:17:27', 'Réservation', '58E671956H660694Y', '2025-05-20 15:17:27'),
(66, 420, 3, 117, 875, '2025-06-24 15:40:07', 'Réservation', '9WP84900H2760332J', '2025-06-24 15:40:07'),
(67, 421, 3, 117, 920, '2025-07-03 17:43:37', 'Réservation', '2FS22725P3329451R', '2025-07-03 17:43:37'),
(68, 422, 3, 117, 300, '2025-07-10 17:10:43', 'Réservation', '04893931NB9995141', '2025-07-10 17:10:43'),
(69, 423, 3, 117, 300, '2025-07-10 17:55:22', 'Réservation', 'TEST_PAYMENT_686ffe89c0694', '2025-07-10 17:55:22'),
(70, 424, 3, 117, 300, '2025-07-10 17:56:25', 'Réservation', 'TEST_PAYMENT_686ffec95d82f', '2025-07-10 17:56:25'),
(71, 425, 3, 117, 130, '2025-07-10 18:00:17', 'Réservation', '4DH33375R3739192Y', '2025-07-10 18:00:17'),
(72, 426, 3, 117, 130, '2025-07-10 18:00:49', 'Réservation', '2U303132CV7312936', '2025-07-10 18:00:49'),
(73, 427, 3, 117, 100, '2025-07-10 18:12:32', 'Réservation', '2M056642PY135662T', '2025-07-10 18:12:32'),
(74, 428, 3, 117, 100, '2025-07-10 18:21:29', 'Réservation', '7YF35218KG740863M', '2025-07-10 18:21:29'),
(75, 429, 1, 117, 160, '2025-07-17 00:00:00', 'Réservation', NULL, '2025-07-17 18:09:26'),
(76, 430, 1, 117, 100, '2025-07-17 00:00:00', 'Réservation', NULL, '2025-07-17 20:17:06'),
(77, 430, 1, 117, 50, '2025-07-17 00:00:00', 'Réservation', NULL, '2025-07-17 20:21:17'),
(78, 430, 1, 117, 120, '2025-07-21 00:00:00', 'Réservation', NULL, '2025-07-21 18:59:43'),
(79, 21, 1, 117, 220, '2025-07-21 00:00:00', 'Réservation', NULL, '2025-07-21 19:29:29');

-- --------------------------------------------------------

--
-- Structure de la table `reservation`
--

DROP TABLE IF EXISTS `reservation`;
CREATE TABLE IF NOT EXISTS `reservation` (
  `id` int NOT NULL AUTO_INCREMENT,
  `client_id` int NOT NULL,
  `vehicule_id` int NOT NULL,
  `mode_reservation_id` int DEFAULT NULL,
  `etat_reservation_id` int DEFAULT NULL,
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `date_reservation` datetime NOT NULL,
  `date_debut` datetime NOT NULL,
  `date_fin` datetime NOT NULL,
  `lieu` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `code_reservation` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `commentaire` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `agence_depart` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `agence_retour` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `prix` double DEFAULT NULL,
  `duree` double DEFAULT NULL,
  `num_devis` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `reference` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `tarif_vehicule` double DEFAULT NULL,
  `prix_options` double DEFAULT NULL,
  `prix_garanties` double DEFAULT NULL,
  `stripe_session_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `archived` tinyint(1) DEFAULT NULL,
  `canceled` tinyint(1) DEFAULT NULL,
  `reported` tinyint(1) NOT NULL,
  `conducteur` tinyint(1) DEFAULT NULL,
  `saisisseur_km_id` int DEFAULT NULL,
  `km_depart` double DEFAULT NULL,
  `km_retour` double DEFAULT NULL,
  `date_km` date DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_42C8495519EB6921` (`client_id`),
  KEY `IDX_42C849554A4A3511` (`vehicule_id`),
  KEY `IDX_42C849556776468B` (`mode_reservation_id`),
  KEY `IDX_42C8495514237FB` (`etat_reservation_id`),
  KEY `IDX_42C84955C1E5BBF9` (`saisisseur_km_id`)
) ENGINE=InnoDB AUTO_INCREMENT=431 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `reservation`
--

INSERT INTO `reservation` (`id`, `client_id`, `vehicule_id`, `mode_reservation_id`, `etat_reservation_id`, `type`, `date_reservation`, `date_debut`, `date_fin`, `lieu`, `code_reservation`, `commentaire`, `agence_depart`, `agence_retour`, `prix`, `duree`, `num_devis`, `reference`, `tarif_vehicule`, `prix_options`, `prix_garanties`, `stripe_session_id`, `archived`, `canceled`, `reported`, `conducteur`, `saisisseur_km_id`, `km_depart`, `km_retour`, `date_km`) VALUES
(20, 117, 1, NULL, NULL, NULL, '2021-11-18 20:44:52', '2021-11-22 20:42:00', '2021-11-30 20:42:00', NULL, 'devisTransformé', NULL, 'AEROPORT DE POINT-A-PITRE', 'AEROPORT DE POINT-A-PITRE', 130, 8, NULL, 'CPTGP2021NOV00001', 0, 30, 100, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(21, 117, 1, NULL, NULL, NULL, '2021-11-10 07:33:40', '2021-11-14 07:32:00', '2021-11-16 07:32:00', NULL, 'devisTransformé', NULL, 'AEROPORT DE POINT-A-PITRE', 'AEROPORT DE POINT-A-PITRE', 620, 2, NULL, 'CPTGP2021NOV00021', 0, 30, 90, 'cs_test_a1fZAVwJ9dlb2dMDuLUj1tmfZfeu30zTJYLKSO8VAuKa4WnHpPe6gCGHFU', 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(22, 117, 2, NULL, NULL, NULL, '2021-11-21 12:48:35', '2021-11-21 14:42:00', '2021-11-22 12:40:00', NULL, 'devisTransformé', NULL, 'AEROPORT DE POINT-A-PITRE', 'AEROPORT DE POINT-A-PITRE', 250, 1, NULL, 'CPTGP2021NOV00022', 0, 60, 190, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(23, 117, 1, NULL, NULL, NULL, '2021-11-21 12:52:56', '2021-11-22 12:52:00', '2021-11-28 12:52:00', NULL, 'devisTransformé', NULL, 'AGENCE DU MOULE', 'GARE MARITIME DE BERGERVIN', 330, 6, NULL, 'CPTGP2021NOV00023', 200, 30, 100, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(24, 117, 1, 2, NULL, NULL, '2022-02-03 20:37:21', '2022-02-04 20:32:00', '2022-02-05 03:55:33', NULL, 'devisTransformé', NULL, 'GARE MARITIME DE BERGERVIN', 'AEROPORT DE POINT-A-PITRE', 705, 1, NULL, 'CPT2022FEV00024', 585, 30, 90, 'cs_test_a1QetvwuQptPnqAP8oCh6HiZ452CnwahtslqJmGiZGFu2IcIOQtpaOKaX4', 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(25, 146, 3, 2, NULL, NULL, '2022-02-04 01:42:56', '2022-02-04 18:41:00', '2022-02-19 18:42:00', NULL, 'devisTransformé', NULL, 'aeroport', 'aeroport', 837, 15, NULL, 'CPT2022FEV00025', 837, 0, 0, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(26, 147, 13, 2, NULL, NULL, '2022-02-04 07:10:07', '2022-02-05 05:08:00', '2022-02-19 05:08:00', NULL, 'devisTransformé', NULL, 'AEROPORT DE POINT-A-PITRE', 'AEROPORT DE POINT-A-PITRE', 675, 14, NULL, 'CPT2022FEV00026', 585, 0, 90, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(27, 147, 1, 2, NULL, NULL, '2022-02-06 13:41:35', '2022-02-07 13:40:00', '2022-02-20 13:40:00', NULL, 'devisTransformé', NULL, 'aeroport', 'aeroport', 250, 13, NULL, 'CPT2022FEV00027', 120, 30, 100, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(29, 147, 2, 2, NULL, NULL, '2022-02-13 16:55:53', '2022-02-14 16:50:00', '2022-02-17 16:50:00', NULL, 'devisTransformé', NULL, 'AEROPORT DE POINT-A-PITRE', 'AEROPORT DE POINT-A-PITRE', 263.7, 3, NULL, 'CPT2022FEV00028', 83.7, 80, 100, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(31, 148, 3, 2, NULL, NULL, '2022-02-20 22:12:30', '2022-03-11 15:30:00', '2022-03-19 17:30:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 238, 8, NULL, 'CPT2022FEV00031', 208, 30, 0, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(32, 145, 8, 2, NULL, NULL, '2022-02-21 17:59:15', '2022-02-27 10:58:00', '2022-04-03 10:58:00', NULL, 'devisTransformé', NULL, 'Anse-bertrand', 'Morne-à-l\'Eau', 182.5, 35, NULL, 'CPT2022FEV00032', 182.5, 0, 0, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(33, 146, 1, 2, NULL, NULL, '2022-02-21 18:02:35', '2022-02-27 11:01:00', '2022-03-13 11:01:00', NULL, 'devisTransformé', NULL, 'Anse-bertrand', 'Aéroport de Point-à-pitre', 585, 14, NULL, 'CPT2022FEV00033', 585, 0, 0, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(36, 146, 1, 2, NULL, NULL, '2022-03-15 18:20:37', '2022-03-18 11:19:00', '2022-03-20 11:19:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 120, 2, NULL, 'CPT2022MAR00034', 120, 0, 0, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(37, 149, 9, 2, NULL, NULL, '2022-03-20 23:59:10', '2022-03-21 16:55:00', '2022-04-04 16:55:00', NULL, 'devisTransformé', NULL, 'aeroport', 'aeroport', 375, 14, NULL, 'CPT2022MAR00037', 375, 0, 0, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(38, 150, 12, 2, NULL, NULL, '2022-03-21 00:03:47', '2022-03-21 17:00:00', '2022-04-04 17:00:00', NULL, 'devisTransformé', NULL, 'aeroport', 'aeroport', 188, 14, NULL, 'CPT2022MAR00038', 188, 0, 0, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(39, 151, 11, 2, NULL, NULL, '2022-03-21 00:08:37', '2022-03-21 17:04:00', '2022-03-25 17:05:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 311.21, 4, NULL, 'CPT2022MAR00039', 311.21, 0, 0, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(40, 152, 10, 2, NULL, NULL, '2022-03-21 00:12:26', '2022-03-20 19:09:00', '2022-03-30 17:09:00', NULL, 'devisTransformé', NULL, 'Sainte-anne', 'Sainte-anne', 375, 10, NULL, 'CPT2022MAR00040', 375, 0, 0, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(41, 153, 13, 2, NULL, NULL, '2022-03-21 00:16:33', '2022-03-21 17:13:00', '2022-04-30 17:13:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 700, 40, NULL, 'CPT2022MAR00041', 700, 0, 0, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(42, 154, 14, 2, NULL, NULL, '2022-03-21 00:21:19', '2022-03-21 17:16:00', '2022-04-30 17:17:00', NULL, 'devisTransformé', NULL, 'Abymes', 'Abymes', 450, 40, NULL, 'CPT2022MAR00042', 450, 0, 0, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(43, 155, 1, 2, NULL, NULL, '2022-03-21 00:24:36', '2022-03-20 20:21:00', '2022-03-28 17:22:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 745, 8, NULL, 'CPT2022MAR00043', 585, 0, 160, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(44, 156, 11, 2, NULL, NULL, '2022-03-21 00:37:48', '2022-03-26 17:33:00', '2022-04-11 17:34:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 300, 16, NULL, 'CPT2022MAR00044', 300, 0, 0, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(46, 158, 10, 2, NULL, NULL, '2022-03-21 01:12:28', '2022-03-31 13:25:00', '2022-04-10 19:45:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 375, 10, NULL, 'CPT2022MAR00046', 375, 0, 0, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(47, 117, 2, 2, NULL, NULL, '2022-03-21 14:16:39', '2022-03-23 13:56:00', '2022-03-25 13:56:00', NULL, 'devisTransformé', NULL, 'Gare Maritime de Bergervin', 'Anse-bertrand', 175, 2, NULL, 'CPT2022MAR00047', 75, 0, 100, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(48, 159, 9, 2, NULL, NULL, '2022-03-22 01:59:12', '2022-04-05 18:53:00', '2022-08-23 17:00:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 4398.55, 140, NULL, 'CPT2022MAR00048', 4398.55, 0, 0, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(49, 160, 3, 2, NULL, NULL, '2022-03-22 02:21:40', '2022-07-25 19:16:00', '2022-08-16 17:46:05', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 300, 22, NULL, 'CPT2022MAR00049', 300, 0, 0, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(50, 146, 2, 2, NULL, NULL, '2022-03-22 02:40:07', '2022-09-01 19:38:00', '2022-11-21 19:38:00', NULL, 'devisTransformé', NULL, 'Gare Maritime de Bergervin', 'Gare Maritime de Bergervin', 686, 81, NULL, 'CPT2022MAR00050', 596, 0, 90, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(52, 4, 3, NULL, NULL, 'test', '2022-03-22 02:49:15', '2022-03-22 08:47:00', '2022-03-25 19:48:00', NULL, 'stopSale', 'test', 'garage', NULL, NULL, 3, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, NULL),
(53, 161, 10, 2, NULL, NULL, '2022-03-28 04:36:02', '2022-04-11 11:00:00', '2022-04-15 17:00:00', NULL, 'devisTransformé', NULL, 'Gosier', 'Gosier', 134, 4, NULL, 'CPT2022MAR00053', 134, 0, 0, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(54, 145, 1, 2, NULL, NULL, '2022-03-29 05:03:13', '2022-03-29 17:01:00', '2022-04-22 12:01:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 1170, 24, NULL, 'CPT2022MAR00054', 1170, 0, 0, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(55, 146, 2, 2, NULL, NULL, '2022-03-29 05:06:03', '2022-03-31 10:03:00', '2022-04-09 15:04:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 225, 9, NULL, 'CPT2022MAR00055', 225, 0, 0, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(56, 146, 2, 2, NULL, NULL, '2022-03-29 05:11:07', '2022-04-10 09:08:00', '2022-04-25 22:09:00', NULL, 'devisTransformé', NULL, 'Gare Maritime de Bergervin', 'Gare Maritime de Bergervin', 435, 15, NULL, 'CPT2022MAR00056', 435, 0, 0, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(57, 4, 3, NULL, NULL, 'abs', '2022-03-29 05:14:12', '2022-03-29 22:13:00', '2022-03-31 22:13:00', NULL, 'stopSale', 'FREIN', 'garage', NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, NULL),
(58, 145, 3, 2, NULL, NULL, '2022-03-29 05:19:38', '2022-04-01 08:17:00', '2022-04-14 11:17:00', NULL, 'devisTransformé', NULL, 'Morne-à-l\'Eau', 'Morne-à-l\'Eau', 275, 13, NULL, 'CPT2022MAR00058', 275, 0, 0, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(59, 147, 3, 2, NULL, NULL, '2022-03-29 05:28:56', '2022-04-15 07:22:00', '2022-04-15 18:23:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 60, 0, NULL, 'CPT2022MAR00059', 60, 0, 0, NULL, 0, 1, 0, NULL, NULL, NULL, NULL, NULL),
(60, 162, 8, 2, NULL, NULL, '2022-03-29 05:43:30', '2022-04-04 09:34:00', '2022-05-04 09:34:00', NULL, 'devisTransformé', NULL, 'Gare maritime de Saint-François', 'Gare maritime de Saint-François', 435, 30, NULL, 'CPT2022MAR00060', 435, 0, 0, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(61, 161, 10, 2, NULL, NULL, '2022-04-11 16:54:50', '2022-04-11 11:00:00', '2022-04-15 17:00:00', NULL, 'devisTransformé', NULL, 'Gosier', 'Aéroport de Point-à-pitre', 137, 4, NULL, 'CPT2022AVR00061', 137, 0, 0, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(62, 150, 12, 2, NULL, NULL, '2022-04-11 16:58:25', '2022-04-11 13:57:00', '2022-04-14 12:57:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 60, 3, NULL, 'CPT2022AVR00062', 60, 0, 0, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(63, 147, 2, 3, NULL, NULL, '2022-05-21 15:30:53', '2022-05-23 14:15:00', '2022-05-25 14:15:00', NULL, 'devisTransformé', NULL, 'Abymes', 'Aéroport de Point-à-pitre', 173, 2, '163', 'WEB2022MAI00063', 33, 140, 0, 'cs_test_a1hci5VRWbFs4KrOy0VvW0QWJBYSzhqBXgLWjsyhBkxwJzHr1QJjhV6iJA', 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(64, 153, 13, 2, NULL, NULL, '2022-05-29 03:33:59', '2022-06-01 09:00:00', '2022-06-30 09:00:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 700, 29, NULL, 'CPT2022MAI00064', 700, 0, 0, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(65, 162, 8, 2, NULL, NULL, '2022-05-29 03:37:40', '2022-06-01 09:00:00', '2022-06-30 09:00:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 300, 29, NULL, 'CPT2022MAI00065', 300, 0, 0, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(66, 150, 10, 2, NULL, NULL, '2022-05-29 03:47:58', '2022-06-01 09:00:00', '2022-06-23 17:00:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 350, 22, NULL, 'CPT2022MAI00066', 350, 0, 0, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(67, 154, 14, 2, NULL, NULL, '2022-05-29 03:50:48', '2022-06-01 09:00:00', '2022-06-30 09:00:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 450, 29, NULL, 'CPT2022MAI00067', 450, 0, 0, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(68, 166, 11, 2, NULL, NULL, '2022-05-29 04:02:46', '2022-05-29 08:00:00', '2022-06-04 18:00:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 250, 6, NULL, 'CPT2022MAI00068', 250, 0, 0, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(70, 167, 12, 2, NULL, NULL, '2022-05-29 04:32:52', '2022-05-29 09:00:00', '2022-06-04 17:00:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 225, 6, NULL, 'CPT2022MAI00069', 225, 0, 0, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(71, 168, 11, 2, NULL, NULL, '2022-05-29 04:41:01', '2022-08-01 14:00:00', '2022-08-07 14:00:00', NULL, 'devisTransformé', NULL, 'aeroport', 'aeroport', 350, 6, NULL, 'CPT2022MAI00071', 350, 0, 0, NULL, 0, 1, 0, NULL, NULL, NULL, NULL, NULL),
(72, 169, 11, 2, NULL, NULL, '2022-06-06 17:55:52', '2022-08-11 08:00:00', '2022-08-31 17:00:00', NULL, 'devisTransformé', NULL, 'aeroport', 'aeroport', 592, 20, NULL, 'CPT2022JUI00072', 562, 30, 0, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(73, 170, 10, 2, NULL, NULL, '2022-06-08 22:39:56', '2022-07-14 10:00:00', '2022-08-12 03:35:46', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 893.15, 29, NULL, 'CPT2022JUI00073', 893.15, 0, 0, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(74, 171, 14, 2, NULL, NULL, '2022-06-10 05:35:06', '2022-07-14 15:00:00', '2022-08-05 15:00:00', NULL, 'devisTransformé', NULL, 'aeroport', 'aeroport', 720, 22, NULL, 'CPT2022JUI00074', 690, 30, 0, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(75, 167, 12, 2, NULL, NULL, '2022-06-11 02:42:11', '2022-06-11 10:00:00', '2022-06-15 17:00:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 225, 4, NULL, 'CPT2022JUI00075', 225, 0, 0, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(76, 156, 12, 2, NULL, NULL, '2022-06-20 15:25:14', '2022-06-21 08:21:00', '2022-07-06 15:00:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 292.95, 15, NULL, 'CPT2022JUI00076', 292.95, 0, 0, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(77, 172, 12, 2, NULL, NULL, '2022-06-20 20:16:06', '2022-07-18 13:10:00', '2022-07-31 13:10:00', NULL, 'devisTransformé', NULL, 'aeroport', 'aeroport', 352.63, 13, NULL, 'CPT2022JUI00077', 352.63, 0, 0, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(78, 173, 11, 2, NULL, NULL, '2022-06-21 15:45:10', '2022-06-21 12:00:00', '2022-06-30 12:00:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 180, 9, NULL, 'CPT2022JUI00078', 180, 0, 0, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(79, 160, 10, 2, NULL, NULL, '2022-06-25 16:33:02', '2022-06-26 16:45:00', '2022-07-01 16:00:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 119.35, 5, NULL, 'CPT2022JUI00079', 119.35, 0, 0, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(80, 157, 12, 2, NULL, NULL, '2022-07-05 02:52:05', '2022-09-11 13:25:00', '2022-09-27 16:00:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Gare Maritime de Bergervin', 669, 16, NULL, 'CPT2022JUIL00080', 669, 0, 0, NULL, 1, 1, 0, NULL, NULL, NULL, NULL, NULL),
(82, 162, 8, 2, NULL, NULL, '2022-07-05 03:05:28', '2022-07-04 21:04:00', '2022-07-31 20:04:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 300, 27, NULL, 'CPT2022JUIL00082', 300, 0, 0, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(83, 174, 1, 2, NULL, NULL, '2022-07-06 17:02:52', '2022-07-08 11:00:00', '2022-08-05 10:48:00', NULL, 'devisTransformé', NULL, 'Moule', 'Moule', 1008, 28, NULL, 'CPT2022JUIL00083', 1008, 0, 0, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(84, 153, 13, 2, NULL, NULL, '2022-07-06 17:08:11', '2022-07-06 11:00:00', '2022-07-31 12:00:00', NULL, 'devisTransformé', NULL, 'Saint-François', 'Saint-François', 660.12, 25, NULL, 'CPT2022JUIL00084', 660.12, 0, 0, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(85, 4, 14, NULL, NULL, 'carroserie', '2022-07-06 17:20:18', '2022-07-07 10:19:00', '2022-07-12 10:19:00', NULL, 'stopSale', 'bas de caisse feux', 'garage', NULL, NULL, 5, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, NULL),
(86, 175, 11, 2, NULL, NULL, '2022-07-07 03:46:18', '2022-07-07 15:20:00', '2022-07-30 16:00:00', NULL, 'devisTransformé', NULL, 'aeroport', 'aeroport', 500, 23, NULL, 'CPT2022JUIL00086', 500, 0, 0, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(87, 176, 12, 2, NULL, NULL, '2022-07-12 00:11:45', '2022-07-12 17:05:00', '2022-07-18 08:05:00', NULL, 'devisTransformé', NULL, 'Pointe-à-pitre', 'Pointe-à-pitre', 150, 6, NULL, 'CPT2022JUIL00087', 150, 0, 0, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(88, 146, 12, 2, NULL, NULL, '2022-07-12 00:20:53', '2022-08-01 14:00:00', '2022-08-12 15:00:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 300, 11, NULL, 'CPT2022JUIL00088', 300, 0, 0, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(90, 4, 2, NULL, NULL, 'abs', '2022-07-14 14:13:50', '2022-07-20 19:43:00', '2022-07-25 19:43:00', NULL, 'stopSale', 'frein abs', 'garage', NULL, NULL, 5, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, NULL),
(93, 162, 8, 2, NULL, NULL, '2022-07-31 20:23:49', '2022-08-01 08:00:00', '2022-08-25 01:30:08', NULL, 'devisTransformé', NULL, 'Moule', 'Moule', 300, 24, NULL, 'CPT2022JUIL00093', 300, 0, 0, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(94, 153, 13, 2, NULL, NULL, '2022-07-31 20:26:26', '2022-08-01 08:00:00', '2022-08-25 15:00:00', NULL, 'devisTransformé', NULL, 'Moule', 'Aéroport de Point-à-pitre', 1130, 24, NULL, 'CPT2022JUIL00094', 1130, 0, 0, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(95, 179, 11, 2, NULL, NULL, '2022-08-03 02:45:45', '2023-03-17 15:15:00', '2023-03-31 19:00:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 364, 14, NULL, 'CPT2022AOU00095', 364, 0, 0, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(96, 160, 1, 2, NULL, NULL, '2022-08-05 17:44:57', '2022-08-05 17:00:00', '2022-08-20 17:00:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 450, 15, NULL, 'CPT2022AOU00096', 450, 0, 0, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, NULL),
(97, 181, 12, 2, NULL, NULL, '2022-08-09 16:52:08', '2022-08-13 10:00:00', '2022-08-28 11:30:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 685, 15, NULL, 'CPT2022AOU00097', 525, 0, 160, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(98, 182, 14, 2, NULL, NULL, '2022-08-10 00:06:25', '2022-08-11 12:55:00', '2022-08-22 10:00:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 464.15, 11, NULL, 'CPT2022AOU00098', 304.15, 0, 160, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(99, 183, 10, 2, NULL, NULL, '2022-08-10 00:21:03', '2022-08-17 12:00:00', '2022-09-11 11:30:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 750, 25, NULL, 'CPT2022AOU00099', 750, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(100, 180, 10, 2, NULL, NULL, '2022-08-12 03:38:19', '2022-08-12 10:00:00', '2022-08-16 10:00:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 100, 4, NULL, 'CPT2022AOU100', 100, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(101, 180, 9, 2, NULL, NULL, '2022-08-19 14:20:05', '2022-08-24 14:00:00', '2022-09-08 14:00:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 375, 15, NULL, 'CPT2022AOU00101', 375, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(102, 159, 14, 2, NULL, NULL, '2022-08-23 00:14:39', '2022-08-24 10:00:00', '2022-12-13 15:00:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 750, 111, NULL, 'CPT2022AOU00102', 750, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(103, 185, 2, 2, NULL, NULL, '2022-08-25 01:32:33', '2022-08-25 08:00:00', '2022-08-29 12:00:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 100, 4, NULL, 'CPT2022AOU00103', 100, 0, 0, NULL, 0, 1, 0, 0, NULL, NULL, NULL, NULL),
(104, 185, 8, 2, NULL, NULL, '2022-08-25 01:33:47', '2022-08-25 08:00:00', '2022-08-30 17:00:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 152, 5, NULL, 'CPT2022AOU00104', 152, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(105, 185, 2, 2, NULL, NULL, '2022-08-25 01:52:12', '2022-08-25 10:00:00', '2022-08-31 12:00:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 152, 6, NULL, 'CPT2022AOU00105', 152, 0, 0, NULL, 0, 1, 0, 0, NULL, NULL, NULL, NULL),
(106, 153, 1, 2, NULL, NULL, '2022-08-25 15:39:49', '2022-08-25 16:00:00', '2022-08-31 15:00:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 282, 6, NULL, 'CPT2022AOU00106', 282, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(107, 159, 14, 2, NULL, NULL, '2022-08-25 15:45:03', '2023-01-03 14:00:00', '2023-07-19 17:00:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Agence du Moule', 930, 197, NULL, 'CPT2022AOU00107', 930, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(108, 184, 11, 2, NULL, NULL, '2022-08-26 21:07:46', '2022-09-08 14:00:00', '2022-09-22 16:00:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Agence du Moule', 334, 14, NULL, 'CPT2022AOU00108', 334, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(109, 4, 12, NULL, NULL, 'carroserie peinture', '2022-08-31 05:14:11', '2022-08-30 22:14:00', '2022-09-08 22:14:00', NULL, 'stopSale', 'Hh', 'garage', NULL, NULL, 9, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, NULL),
(110, 186, 11, 2, NULL, NULL, '2022-09-02 19:01:08', '2022-09-02 13:00:00', '2022-09-06 16:00:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 100, 4, NULL, 'CPT2022SEP00110', 100, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(111, 157, 12, 2, NULL, NULL, '2022-09-02 19:21:03', '2022-09-11 13:25:00', '2022-09-25 14:00:23', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Gare Maritime de Bergervin', 334, 14, NULL, 'CPT2022SEP00111', 334, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(112, 187, 13, 2, NULL, NULL, '2022-09-02 22:38:17', '2022-09-05 08:30:00', '2022-09-09 17:00:00', NULL, 'devisTransformé', NULL, 'Gosier', 'Gosier', 120, 4, NULL, 'CPT2022SEP00112', 120, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(113, 162, 8, 2, NULL, NULL, '2022-09-05 03:30:15', '2022-09-05 13:25:00', '2022-09-25 18:20:57', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Agence du Moule', 300, 20, NULL, 'CPT2022SEP00113', 300, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(114, 188, 9, 2, NULL, NULL, '2022-09-08 15:49:50', '2022-09-08 20:39:00', '2022-09-09 17:39:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 40, 1, NULL, 'CPT2022SEP00114', 40, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(115, 153, 13, 2, NULL, NULL, '2022-09-11 23:13:13', '2022-09-11 16:26:00', '2022-10-01 16:10:00', NULL, 'devisTransformé', NULL, 'Saint-François', 'Saint-François', 800, 20, NULL, 'CPT2022SEP00115', 800, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(116, 4, 1, NULL, NULL, 'carroserie', '2022-09-11 23:18:59', '2022-09-12 16:17:00', '2022-09-18 16:17:00', NULL, 'stopSale', 'par choc', 'garage', NULL, NULL, 6, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, NULL),
(117, 189, 9, 2, NULL, NULL, '2022-09-11 23:32:56', '2022-09-11 16:25:00', '2022-10-09 16:20:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 669, 28, NULL, 'CPT2022SEP00117', 669, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(118, 190, 10, 2, NULL, NULL, '2022-09-12 15:38:55', '2022-09-12 09:00:00', '2022-09-14 09:00:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 30, 2, NULL, 'CPT2022SEP00118', 30, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(119, 156, 10, 2, NULL, NULL, '2022-09-12 15:41:16', '2022-09-14 14:00:00', '2022-10-05 14:00:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 420, 21, NULL, 'CPT2022SEP00119', 420, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(120, 190, 11, 2, NULL, NULL, '2022-09-16 05:41:43', '2022-09-23 09:00:00', '2022-09-25 09:01:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 80, 2, NULL, 'CPT2022SEP00120', 30, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(121, 162, 12, 2, NULL, NULL, '2022-09-25 18:25:36', '2022-09-25 15:00:00', '2022-09-30 16:00:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 75, 5, NULL, 'CPT2022SEP00121', 75, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(122, 4, 8, NULL, NULL, 'fuite huile', '2022-09-25 22:59:01', '2022-09-26 08:57:00', '2022-09-30 15:57:00', NULL, 'stopSale', 'fuite huile commodo', 'garage', NULL, NULL, 4, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, NULL),
(123, 153, 13, 2, NULL, NULL, '2022-10-02 04:00:38', '2022-10-01 22:58:00', '2022-10-12 02:13:56', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Moule', 700, 11, NULL, 'CPT2022OCT00123', 700, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(124, 162, 12, 2, NULL, NULL, '2022-10-02 04:02:26', '2022-10-01 22:01:00', '2022-10-05 10:01:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Moule', 167.2, 4, NULL, 'CPT2022OCT00124', 167.2, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(125, 184, 11, 2, NULL, NULL, '2022-10-02 04:06:57', '2022-10-01 21:09:00', '2022-11-15 16:00:23', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Moule', 669, 45, NULL, 'CPT2022OCT00125', 669, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(126, 162, 8, 2, NULL, NULL, '2022-10-05 01:45:52', '2022-10-05 10:00:00', '2022-10-31 18:45:00', NULL, 'devisTransformé', NULL, 'Abymes', 'Abymes', 350, 26, NULL, 'CPT2022OCT00126', 300, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(127, 4, 10, NULL, NULL, 'carroserie', '2022-10-05 01:54:34', '2022-10-05 16:02:00', '2022-10-10 17:00:00', NULL, 'stopSale', 'pompe a essence carroserie', 'garage', NULL, NULL, 5, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, NULL),
(128, 191, 12, 2, NULL, NULL, '2022-10-06 02:29:07', '2022-10-06 15:20:00', '2022-12-11 15:00:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 1220.34, 66, NULL, 'CPT2022OCT00128', 1220.34, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(129, 189, 9, 2, NULL, NULL, '2022-10-12 01:43:39', '2022-10-11 19:00:00', '2022-11-09 17:00:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 450, 29, NULL, 'CPT2022OCT00129', 450, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(130, 153, 1, 2, NULL, NULL, '2022-10-12 02:15:22', '2022-10-11 19:20:00', '2022-10-31 16:00:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 753, 20, NULL, 'CPT2022OCT00130', 753, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(131, 4, 10, NULL, NULL, 'carroserie', '2022-10-12 02:17:04', '2022-10-11 19:20:00', '2022-10-14 15:00:00', NULL, 'stopSale', 'Peinture', 'garage', NULL, NULL, 3, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, NULL),
(132, 174, 10, 2, NULL, NULL, '2022-10-15 03:45:39', '2022-10-14 21:44:00', '2022-10-21 13:03:44', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 167.2, 7, NULL, 'CPT2022OCT00132', 167.2, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(133, 192, 10, 2, NULL, NULL, '2022-10-21 20:06:11', '2022-10-21 17:00:00', '2022-11-10 16:00:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 669, 20, NULL, 'CPT2022OCT00133', 669, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(134, 162, 8, 2, NULL, NULL, '2022-10-26 16:30:08', '2022-11-01 08:00:00', '2022-12-23 18:30:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 480, 52, NULL, 'CPT2022OCT00134', 480, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(135, 193, 11, 2, NULL, NULL, '2022-10-26 22:18:09', '2022-11-18 15:09:00', '2022-11-26 17:09:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 319, 8, NULL, 'CPT2022OCT00135', 319, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(136, 150, 10, 2, NULL, NULL, '2022-11-05 20:29:13', '2022-11-26 18:26:00', '2023-04-13 15:26:00', NULL, 'devisTransformé', NULL, 'Morne-à-l\'Eau', 'Morne-à-l\'Eau', 1500, 138, NULL, 'CPT2022NOV00136', 1500, 0, 0, NULL, 0, 1, 0, 0, NULL, NULL, NULL, NULL),
(137, 150, 10, 2, NULL, NULL, '2022-11-05 20:40:56', '2022-11-26 18:39:00', '2023-04-13 16:39:00', NULL, 'devisTransformé', NULL, 'Morne-à-l\'Eau', 'Morne-à-l\'Eau', 1500, 138, NULL, 'CPT2022NOV00137', 1500, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(138, 194, 11, 2, NULL, NULL, '2022-11-08 13:35:45', '2023-01-16 06:26:00', '2023-02-15 06:26:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 930, 30, NULL, 'CPT2022NOV00138', 930, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(139, 195, 12, 2, NULL, NULL, '2022-11-09 00:35:00', '2022-12-23 17:29:00', '2023-01-07 17:29:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 389, 15, NULL, 'CPT2022NOV00139', 389, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(140, 192, 10, 2, NULL, NULL, '2022-11-11 18:03:30', '2022-11-11 11:05:00', '2022-11-26 18:00:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 319, 15, NULL, 'CPT2022NOV00140', 319, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(141, 189, 9, 2, NULL, NULL, '2022-11-11 18:04:35', '2022-11-11 11:05:00', '2022-12-12 16:00:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 638, 31, NULL, 'CPT2022NOV00141', 638, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(142, 153, 1, 2, NULL, NULL, '2022-11-11 18:06:11', '2022-11-11 11:07:00', '2022-11-30 17:00:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 750, 19, NULL, 'CPT2022NOV00142', 750, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(143, 4, 13, NULL, NULL, 'Problème voyant', '2022-11-11 18:10:19', '2022-11-11 11:15:00', '2022-11-19 11:07:00', NULL, 'stopSale', 'Echangeur d\'air à remplacer', 'garage', NULL, NULL, 8, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, NULL),
(144, 196, 13, 2, NULL, NULL, '2022-11-19 02:15:48', '2023-01-18 19:08:00', '2023-02-15 19:09:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 1170, 28, NULL, 'CPT2022NOV00144', 1170, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(145, 167, 13, 2, NULL, NULL, '2022-11-19 18:51:07', '2022-11-19 14:00:00', '2022-11-27 16:00:00', NULL, 'devisTransformé', NULL, 'Moule', 'Moule', 200, 8, NULL, 'CPT2022NOV00145', 200, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(146, 156, 9, 2, NULL, NULL, '2022-11-20 16:19:00', '2023-01-15 09:17:00', '2023-01-30 09:17:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 469, 15, NULL, 'CPT2022NOV00146', 469, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(147, 197, 12, 2, NULL, NULL, '2022-11-20 16:25:10', '2023-01-16 09:20:00', '2023-01-21 09:20:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 225, 5, NULL, 'CPT2022NOV00147', 225, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(148, 192, 11, 2, NULL, NULL, '2022-11-24 01:36:47', '2022-11-26 23:32:00', '2022-12-11 18:32:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 319, 15, NULL, 'CPT2022NOV00148', 319, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(149, 153, 1, 2, NULL, NULL, '2022-11-24 02:00:26', '2022-12-01 18:57:00', '2022-12-31 18:57:00', NULL, 'devisTransformé', NULL, 'Saint-François', 'Saint-François', 759.48, 30, NULL, 'CPT2022NOV00149', 759.48, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(150, 198, 11, 2, NULL, NULL, '2022-11-24 21:13:47', '2022-12-12 14:09:00', '2022-12-17 14:09:00', NULL, 'devisTransformé', NULL, 'Moule', 'Moule', 180, 5, NULL, 'CPT2022NOV00150', 180, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(151, 199, 12, 2, NULL, NULL, '2022-11-28 14:55:21', '2023-03-22 07:48:00', '2023-03-30 07:49:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 256, 8, NULL, 'CPT2022NOV00151', 256, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(152, 200, 12, 2, NULL, NULL, '2022-12-02 04:19:36', '2023-02-06 15:16:00', '2023-03-06 21:17:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 1047, 28, NULL, 'CPT2022DEC00152', 837, 0, 160, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(153, 167, 13, 2, NULL, NULL, '2022-12-02 21:04:43', '2022-12-02 14:07:00', '2022-12-10 14:02:00', NULL, 'devisTransformé', NULL, 'Moule', 'Moule', 525, 8, NULL, 'CPT2022DEC00153', 525, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(154, 201, 9, 2, NULL, NULL, '2022-12-06 03:28:16', '2023-02-08 20:21:00', '2023-02-23 20:21:00', NULL, 'devisTransformé', NULL, 'Moule', 'Moule', 420, 15, NULL, 'CPT2022DEC00154', 420, 0, 0, NULL, 0, 1, 0, 0, NULL, NULL, NULL, NULL),
(155, 155, 13, 2, NULL, NULL, '2022-12-06 04:51:51', '2023-04-09 21:50:00', '2023-04-23 21:50:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 604, 14, NULL, 'CPT2022DEC00155', 554, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(156, 192, 12, 2, NULL, NULL, '2022-12-09 06:07:27', '2022-12-11 23:05:00', '2022-12-18 23:06:00', NULL, 'devisTransformé', NULL, 'Morne-à-l\'Eau', 'Morne-à-l\'Eau', 244.5, 7, NULL, 'CPT2022DEC00156', 194.5, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(157, 167, 9, 2, NULL, NULL, '2022-12-09 06:09:33', '2022-12-13 02:08:00', '2022-12-20 23:08:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 244.5, 7, NULL, 'CPT2022DEC00157', 194.5, 0, 0, NULL, 0, 1, 0, 1, NULL, NULL, NULL, NULL),
(158, 202, 11, 2, NULL, NULL, '2022-12-09 14:47:52', '2022-12-19 07:41:00', '2022-12-29 07:41:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 439, 10, NULL, 'CPT2022DEC00158', 389, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(159, 203, 14, 2, NULL, NULL, '2022-12-09 23:49:42', '2022-12-19 16:45:00', '2022-12-24 16:45:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 400, 5, NULL, 'CPT2022DEC00159', 350, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(160, 204, 14, 2, NULL, NULL, '2022-12-11 16:32:20', '2022-12-26 09:23:00', '2022-12-29 15:46:38', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Moule', 344, 3, NULL, 'CPT2022DEC00160', 294, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(161, 205, 14, 2, NULL, NULL, '2022-12-14 22:45:57', '2022-12-14 16:41:00', '2022-12-19 15:41:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 244.5, 5, NULL, 'CPT2022DEC00161', 194.5, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(162, 206, 8, 2, NULL, NULL, '2022-12-27 00:43:27', '2022-12-28 17:40:00', '2023-01-07 17:40:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 111, 10, NULL, 'CPT2022DEC00162', 111, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(163, 206, 12, 2, NULL, NULL, '2022-12-27 00:53:31', '2023-01-07 20:51:00', '2023-01-16 08:03:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Aéroport de Point-à-pitre', 229, 9, NULL, 'CPT2022DEC00163', 229, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(164, 206, 12, 2, NULL, NULL, '2022-12-27 00:55:19', '2023-01-21 17:54:00', '2023-01-28 17:54:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Agence du Moule', 220, 7, NULL, 'CPT2022DEC00164', 220, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(165, 204, 11, 2, NULL, NULL, '2022-12-29 15:45:49', '2022-12-29 23:44:00', '2023-01-02 20:44:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 194.5, 4, NULL, 'CPT2022DEC00165', 194.5, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(166, 167, 11, 2, NULL, NULL, '2023-01-04 04:46:18', '2023-01-03 21:50:00', '2023-01-15 16:00:00', NULL, 'devisTransformé', NULL, 'Moule', 'Aéroport de Point-à-pitre', 240, 12, NULL, 'CPT2023JAN00166', 240, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(167, 153, 1, 2, NULL, NULL, '2023-01-04 04:49:07', '2023-01-03 21:50:00', '2023-01-31 18:00:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Aéroport de Point-à-pitre', 759.48, 28, NULL, 'CPT2023JAN00167', 759.48, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(168, 207, 13, 2, NULL, NULL, '2023-01-04 04:53:38', '2023-01-04 16:30:00', '2023-01-09 16:30:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 150, 5, NULL, 'CPT2023JAN00168', 150, 0, 0, NULL, 0, 1, 0, 0, NULL, NULL, NULL, NULL),
(169, 207, 13, 2, NULL, NULL, '2023-01-04 21:34:46', '2023-01-04 16:30:00', '2023-01-08 08:12:33', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 292, 4, NULL, 'CPT2023JAN00169', 292, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(170, 208, 13, 2, NULL, NULL, '2023-01-04 21:37:47', '2023-02-16 15:00:00', '2023-02-28 16:00:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 462, 12, NULL, 'CPT2023JAN00170', 462, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(171, 162, 8, 2, NULL, NULL, '2023-01-08 18:11:19', '2023-01-08 18:10:00', '2023-01-31 20:00:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 300, 23, NULL, 'CPT2023JAN00171', 300, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(172, 174, 13, 2, NULL, NULL, '2023-01-08 18:16:00', '2023-01-08 12:00:00', '2023-01-12 16:00:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 104, 4, NULL, 'CPT2023JAN00172', 104, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(173, 209, 11, 2, NULL, NULL, '2023-01-10 02:18:05', '2023-02-25 19:14:00', '2023-03-11 19:15:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 470, 14, NULL, 'CPT2023JAN00173', 420, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(174, 210, 9, 2, NULL, NULL, '2023-01-12 20:51:19', '2023-02-27 13:48:00', '2023-03-27 13:48:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 550, 28, NULL, 'CPT2023JAN00174', 500, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(175, 211, 12, 2, NULL, NULL, '2023-01-13 23:15:56', '2023-05-01 15:00:00', '2023-05-30 16:12:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 449, 29, NULL, 'CPT2023JAN00175', 449, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(176, 212, 11, 2, NULL, NULL, '2023-01-14 23:49:31', '2023-02-15 16:46:00', '2023-02-22 16:46:00', NULL, 'devisTransformé', NULL, 'Moule', 'Moule', 287, 7, NULL, 'CPT2023JAN00176', 237, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(177, 213, 12, 2, NULL, NULL, '2023-01-17 21:24:26', '2023-01-29 14:21:00', '2023-02-06 14:22:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 519, 8, NULL, 'CPT2023JAN00177', 469, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(178, 4, 9, NULL, NULL, 'carroserie', '2023-01-18 05:34:14', '2023-01-31 22:32:00', '2023-02-08 11:32:00', NULL, 'stopSale', 'BAS DE CAISSE HAYON PEINTURE', 'garage', NULL, NULL, 8, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, NULL),
(179, 214, 9, 2, NULL, NULL, '2023-01-22 16:50:21', '2023-03-27 20:44:00', '2023-04-01 18:46:39', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 425, 5, NULL, 'CPT2023JAN00179', 375, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(180, 215, 13, 2, NULL, NULL, '2023-01-24 23:05:26', '2023-03-09 16:02:00', '2023-03-20 16:03:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Gare Maritime de Bergervin', 585, 11, NULL, 'CPT2023JAN00180', 585, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(181, 216, 12, 2, NULL, NULL, '2023-01-25 15:12:03', '2023-03-08 08:09:00', '2023-03-20 08:09:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 425, 12, NULL, 'CPT2023JAN00181', 375, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(182, 184, 11, 2, NULL, NULL, '2023-02-05 18:48:04', '2023-05-08 11:46:00', '2023-06-15 17:00:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 449, 38, NULL, 'CPT2023FEV00182', 449, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(183, 162, 2, 2, NULL, NULL, '2023-02-08 14:12:02', '2023-02-08 07:16:00', '2023-03-05 07:11:00', NULL, 'devisTransformé', NULL, 'Moule', 'Moule', 779, 25, NULL, 'CPT2023FEV00183', 729, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(184, 212, 9, 2, NULL, NULL, '2023-02-12 15:57:20', '2023-02-13 17:30:00', '2023-02-15 17:30:00', NULL, 'devisTransformé', NULL, 'Moule', 'Agence du Moule', 135, 2, NULL, 'CPT2023FEV00184', 85, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(185, 4, 9, NULL, NULL, 'carroserie', '2023-02-12 15:59:54', '2023-02-16 08:59:00', '2023-02-26 08:59:00', NULL, 'stopSale', 'vitre porte', 'garage', NULL, NULL, 10, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, NULL),
(186, 206, 12, 2, NULL, NULL, '2023-02-26 17:55:58', '2023-04-10 10:54:00', '2023-04-23 10:54:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 275, 13, NULL, 'CPT2023FEV00186', 275, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(187, 160, 13, 2, NULL, NULL, '2023-03-14 16:03:26', '2023-07-21 09:01:00', '2023-08-18 09:02:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 1098, 28, NULL, 'CPT2023MAR00187', 1098, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(188, 217, 8, 2, NULL, NULL, '2023-03-14 16:12:48', '2023-12-07 09:07:00', '2023-12-21 15:07:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 276, 14, NULL, 'CPT2023MAR00188', 276, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(189, 162, 8, 2, NULL, NULL, '2023-03-27 01:55:27', '2023-04-01 08:00:00', '2023-05-18 15:00:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 435, 47, NULL, 'CPT2023MAR00189', 435, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(190, 214, 11, 2, NULL, NULL, '2023-04-01 18:49:25', '2023-04-01 12:01:00', '2023-04-11 15:00:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 275, 10, NULL, 'CPT2023AVR00190', 275, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(191, 156, 9, 2, NULL, NULL, '2023-04-16 01:59:16', '2023-05-15 18:55:00', '2023-05-30 18:55:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 224, 15, NULL, 'CPT2023AVR00191', 224, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(192, 210, 10, 2, NULL, NULL, '2023-04-16 02:04:45', '2023-05-18 19:00:00', '2023-05-25 19:00:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 112.5, 7, NULL, 'CPT2023AVR00192', 112.5, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(193, 218, 13, 2, NULL, NULL, '2023-04-20 06:18:37', '2023-04-28 23:15:00', '2023-05-12 23:15:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 604, 14, NULL, 'CPT2023AVR00193', 554, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(194, 145, 10, 2, NULL, NULL, '2023-04-21 19:11:59', '2023-07-21 08:56:00', '2023-08-06 08:56:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 750, 16, NULL, 'CPT2023AVR00194', 750, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(195, 219, 10, 2, NULL, NULL, '2023-05-04 00:12:44', '2023-05-08 17:09:00', '2023-05-18 17:09:00', NULL, 'devisTransformé', NULL, 'Moule', 'Moule', 224, 10, NULL, 'CPT2023MAI00195', 224, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(196, 4, 1, NULL, NULL, 'casse moteur', '2023-05-04 00:14:18', '2023-05-04 17:13:00', '2023-05-15 17:13:00', NULL, 'stopSale', 'changement moteur', 'garage', NULL, NULL, 11, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, NULL),
(197, 220, 9, 2, NULL, NULL, '2023-05-05 21:33:30', '2023-05-05 15:00:00', '2023-05-14 14:30:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 180, 9, NULL, 'CPT2023MAI00197', 180, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(198, 162, 8, 2, NULL, NULL, '2023-05-18 15:26:43', '2023-06-06 08:25:00', '2023-12-06 08:25:00', NULL, 'devisTransformé', NULL, 'Moule', 'Moule', 330, 183, NULL, 'CPT2023MAI00198', 330, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(199, 150, 14, 2, NULL, NULL, '2023-05-18 15:31:24', '2023-07-20 10:00:00', '2023-10-28 16:00:00', NULL, 'devisTransformé', NULL, 'Morne-à-l\'Eau', 'Morne-à-l\'Eau', 750, 100, NULL, 'CPT2023MAI00199', 750, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(200, 221, 9, 2, NULL, NULL, '2023-05-27 01:42:34', '2023-07-22 15:00:00', '2023-08-20 15:00:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 812, 29, NULL, 'CPT2023MAI00200', 812, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(201, 145, 11, 2, NULL, NULL, '2023-05-27 01:44:12', '2023-07-09 15:00:00', '2023-07-19 15:00:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 375, 10, NULL, 'CPT2023MAI00201', 375, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(202, 222, 12, 2, NULL, NULL, '2023-05-27 04:23:41', '2023-08-04 21:18:00', '2023-08-29 21:18:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 750, 25, NULL, 'CPT2023MAI00202', 750, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(203, 223, 8, 2, NULL, NULL, '2023-05-29 00:52:12', '2023-05-31 17:46:00', '2023-06-06 05:46:00', NULL, 'devisTransformé', NULL, 'Saint-François', 'Saint-François', 132.5, 6, NULL, 'CPT2023MAI00203', 82.5, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(204, 215, 13, 2, NULL, NULL, '2023-05-30 06:17:19', '2023-06-17 13:25:00', '2023-06-21 23:16:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 232, 4, NULL, 'CPT2023MAI00204', 232, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(205, 224, 11, 2, NULL, NULL, '2023-06-02 20:38:35', '2023-07-24 13:33:00', '2023-08-08 13:33:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 425, 15, NULL, 'CPT2023JUI00205', 375, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(206, 225, 10, 2, NULL, NULL, '2023-06-08 06:03:11', '2023-06-08 22:58:00', '2023-06-12 22:58:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 162.5, 4, NULL, 'CPT2023JUI00206', 112.5, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(207, 226, 11, 2, NULL, NULL, '2023-06-10 17:37:09', '2023-08-08 18:32:00', '2023-09-01 10:32:00', NULL, 'devisTransformé', NULL, 'Moule', 'Moule', 800, 24, NULL, 'CPT2023JUI00207', 750, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(208, 167, 12, 2, NULL, NULL, '2023-06-10 21:22:15', '2023-06-10 15:00:00', '2023-07-10 17:00:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 450, 30, NULL, 'CPT2023JUI00208', 450, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(209, 227, 11, 2, NULL, NULL, '2023-06-16 14:52:09', '2023-06-18 10:00:00', '2023-06-27 15:00:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 224, 9, NULL, 'CPT2023JUI00209', 224, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(210, 228, 1, 2, NULL, NULL, '2023-06-20 13:36:26', '2023-07-28 06:31:00', '2023-08-26 06:32:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 1148, 29, NULL, 'CPT2023JUI00210', 1098, 0, 0, NULL, 0, 1, 0, 1, NULL, NULL, NULL, NULL),
(211, 225, 9, 2, NULL, NULL, '2023-06-20 13:39:41', '2023-06-20 07:38:00', '2023-07-22 06:38:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 449, 32, NULL, 'CPT2023JUI00211', 449, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(212, 146, 10, 2, NULL, NULL, '2023-06-27 18:20:09', '2023-08-14 11:16:00', '2023-09-03 11:16:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 750, 20, NULL, 'CPT2023JUI00212', 750, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(213, 229, 13, 2, NULL, NULL, '2023-07-03 15:35:39', '2023-09-06 08:31:00', '2023-10-02 08:31:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 1040, 26, NULL, 'CPT2023JUIL00213', 990, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(214, 174, 11, 2, NULL, NULL, '2023-07-08 02:02:11', '2023-07-07 20:02:00', '2023-07-09 07:01:00', NULL, 'devisTransformé', NULL, 'Moule', 'Moule', 125, 2, NULL, 'CPT2023JUIL00214', 75, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(215, 167, 12, 2, NULL, NULL, '2023-07-20 15:47:13', '2023-07-20 11:00:00', '2023-08-04 10:46:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 200, 15, NULL, 'CPT2023JUIL00215', 200, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(216, 167, 1, 2, NULL, NULL, '2023-07-20 16:00:20', '2023-08-06 17:30:00', '2023-09-10 17:00:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 1130, 35, NULL, 'CPT2023JUIL00216', 1130, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(217, 230, 10, 2, NULL, NULL, '2023-08-01 15:06:22', '2023-08-06 10:05:00', '2023-08-14 08:05:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 425, 8, NULL, 'CPT2023AOU00217', 375, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(218, 231, 2, 2, NULL, NULL, '2023-08-01 15:49:45', '2023-08-02 08:42:00', '2023-08-31 08:42:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 658, 29, NULL, 'CPT2023AOU00218', 608, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(219, 226, 3, 2, NULL, NULL, '2023-08-06 00:29:03', '2023-08-06 17:28:00', '2023-08-08 17:28:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 75, 2, NULL, 'CPT2023AOU00219', 75, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(220, 232, 13, 2, NULL, NULL, '2023-08-09 04:42:11', '2023-12-11 21:36:00', '2024-01-13 00:34:18', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 1101, 33, NULL, 'CPT2023AOU00220', 1051, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL);
INSERT INTO `reservation` (`id`, `client_id`, `vehicule_id`, `mode_reservation_id`, `etat_reservation_id`, `type`, `date_reservation`, `date_debut`, `date_fin`, `lieu`, `code_reservation`, `commentaire`, `agence_depart`, `agence_retour`, `prix`, `duree`, `num_devis`, `reference`, `tarif_vehicule`, `prix_options`, `prix_garanties`, `stripe_session_id`, `archived`, `canceled`, `reported`, `conducteur`, `saisisseur_km_id`, `km_depart`, `km_retour`, `date_km`) VALUES
(221, 156, 12, 2, NULL, NULL, '2023-08-09 04:46:39', '2023-09-04 10:44:00', '2023-09-17 21:44:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 334, 13, NULL, 'CPT2023AOU00221', 334, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(222, 233, 9, 2, NULL, NULL, '2023-08-11 01:24:26', '2023-08-26 18:19:00', '2023-09-12 18:19:00', NULL, 'devisTransformé', NULL, 'Morne-à-l\'Eau', 'Aéroport de Point-à-pitre', 750, 17, NULL, 'CPT2023AOU00222', 750, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(223, 234, 1, 2, NULL, NULL, '2023-08-12 03:03:00', '2024-01-24 20:00:00', '2024-02-03 20:00:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 635, 10, NULL, 'CPT2023AOU00223', 585, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(224, 235, 13, 2, NULL, NULL, '2023-08-12 03:06:26', '2024-01-24 20:03:00', '2024-01-26 03:40:12', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 635, 2, NULL, 'CPT2023AOU00224', 585, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(225, 230, 13, 2, NULL, NULL, '2023-08-15 03:41:12', '2023-08-18 23:39:00', '2023-09-06 03:40:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 1180, 19, NULL, 'CPT2023AOU00225', 1130, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(226, 236, 9, 2, NULL, NULL, '2023-08-21 16:19:35', '2023-08-22 09:17:00', '2023-08-26 09:17:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 237.5, 4, NULL, 'CPT2023AOU00226', 187.5, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(227, 237, 2, 2, NULL, NULL, '2023-08-21 18:28:29', '2024-01-28 11:24:00', '2024-02-03 11:24:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 202, 6, NULL, 'CPT2023AOU00227', 202, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(228, 225, 10, 2, NULL, NULL, '2023-09-06 01:56:55', '2023-09-05 18:58:00', '2023-09-08 20:56:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 70, 3, NULL, 'CPT2023SEP00228', 70, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(229, 238, 11, 2, NULL, NULL, '2023-09-06 02:01:46', '2023-09-05 20:57:00', '2023-09-14 18:59:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 384, 9, NULL, 'CPT2023SEP00229', 334, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(230, 239, 16, 2, NULL, NULL, '2023-09-18 16:23:46', '2024-01-02 09:22:00', '2024-01-31 09:22:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 0, 29, NULL, 'CPT2023SEP00230', 0, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(231, 167, 9, 2, NULL, NULL, '2023-09-30 21:01:51', '2023-09-30 14:04:00', '2023-10-27 22:39:55', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 719, 27, NULL, 'CPT2023SEP00231', 669, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(232, 225, 10, 2, NULL, NULL, '2023-09-30 21:03:32', '2023-09-30 15:02:00', '2023-10-16 20:02:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 669, 16, NULL, 'CPT2023SEP00232', 669, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(233, 240, 12, 2, NULL, NULL, '2023-10-05 20:53:45', '2023-12-12 13:50:00', '2023-12-29 17:25:53', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 778, 17, NULL, 'CPT2023OCT00233', 778, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(234, 149, 11, 2, NULL, NULL, '2023-10-05 21:04:26', '2023-10-13 14:01:00', '2023-10-28 14:01:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 384, 15, NULL, 'CPT2023OCT00234', 334, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(235, 226, 11, 2, NULL, NULL, '2023-10-08 16:34:59', '2023-12-17 09:33:00', '2023-12-31 09:33:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Moule', 389, 14, NULL, 'CPT2023OCT00235', 389, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(236, 241, 12, 2, NULL, NULL, '2023-10-12 07:09:01', '2023-10-12 00:07:00', '2023-10-17 18:05:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 167.2, 5, NULL, 'CPT2023OCT00236', 167.2, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(237, 242, 15, 2, NULL, NULL, '2023-10-24 21:01:43', '2024-08-12 01:57:00', '2024-08-24 01:58:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 50, 12, NULL, 'CPT2023OCT00237', 0, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(238, 167, 12, 2, NULL, NULL, '2023-10-27 22:42:03', '2023-10-27 16:00:00', '2023-12-10 16:00:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 450, 44, NULL, 'CPT2023OCT00238', 450, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(239, 150, 1, 2, NULL, NULL, '2023-10-27 22:44:24', '2023-10-28 16:00:00', '2023-11-10 16:00:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 495, 13, NULL, 'CPT2023OCT00239', 495, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(240, 225, 10, 2, NULL, NULL, '2023-10-27 22:46:03', '2023-10-27 16:00:00', '2023-11-07 16:00:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 334, 11, NULL, 'CPT2023OCT00240', 334, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(241, 243, 1, 2, NULL, NULL, '2023-10-30 15:42:58', '2023-12-20 18:37:00', '2024-01-02 08:37:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 525, 13, NULL, 'CPT2023OCT00241', 525, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(242, 244, 11, 2, NULL, NULL, '2023-11-07 02:44:55', '2023-11-09 15:15:00', '2023-11-16 17:00:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 233, 7, NULL, 'CPT2023NOV00242', 233, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(243, 4, 9, NULL, NULL, 'VENDU', '2023-11-07 02:47:09', '2023-11-02 14:45:00', '2030-12-06 19:46:00', NULL, 'stopSale', 'VEHICULE VENDU', 'garage', NULL, NULL, 2591, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, NULL),
(244, 4, 14, NULL, NULL, 'VENDU', '2023-11-07 02:53:35', '2023-10-31 14:52:00', '2030-12-31 19:53:00', NULL, 'stopSale', 'VEHICULE VENDU', 'garage', NULL, NULL, 2618, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, NULL),
(245, 162, 10, 2, NULL, NULL, '2023-11-22 17:29:41', '2023-12-06 10:27:00', '2023-12-14 10:27:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 439, 8, NULL, 'CPT2023NOV00245', 389, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(246, 162, 8, 2, NULL, NULL, '2023-11-22 17:34:22', '2024-01-03 10:33:00', '2024-02-22 10:33:00', NULL, 'devisTransformé', NULL, 'Moule', 'Moule', 810, 50, NULL, 'CPT2023NOV00246', 810, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(247, 167, 1, 2, NULL, NULL, '2023-11-22 17:53:54', '2023-12-11 17:00:00', '2023-12-19 10:52:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 525, 8, NULL, 'CPT2023NOV00247', 525, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(248, 217, 11, 2, NULL, NULL, '2023-12-03 19:03:51', '2023-12-04 15:00:00', '2023-12-06 17:00:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Sainte-anne', 78, 2, NULL, 'CPT2023DEC00248', 78, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(249, 245, 18, 2, NULL, NULL, '2023-12-04 17:28:52', '2024-02-08 10:25:00', '2024-02-18 10:25:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 0, 10, NULL, 'CPT2023DEC00249', 0, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(250, 167, 10, 2, NULL, NULL, '2023-12-11 16:05:21', '2023-12-19 17:00:00', '2024-04-11 17:00:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 778, 114, NULL, 'CPT2023DEC00250', 778, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(251, 248, 11, 2, NULL, NULL, '2023-12-12 19:11:17', '2024-01-19 12:04:00', '2024-02-07 12:04:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Gare Maritime de Bergervin', 930, 19, NULL, 'CPT2023DEC00251', 930, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(252, 248, 11, 2, NULL, NULL, '2023-12-12 19:12:56', '2024-02-11 12:11:00', '2024-02-12 12:12:00', NULL, 'devisTransformé', NULL, 'Gare Maritime de Bergervin', 'Aéroport de Point-à-pitre', 85, 1, NULL, 'CPT2023DEC00252', 85, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(253, 249, 10, 2, NULL, NULL, '2023-12-13 19:25:25', '2023-12-15 12:20:00', '2023-12-19 12:20:00', NULL, 'devisTransformé', NULL, 'Moule', 'Moule', 244.5, 4, NULL, 'CPT2023DEC00253', 194.5, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(254, 249, 8, 2, NULL, NULL, '2023-12-13 19:29:10', '2023-12-22 09:01:00', '2024-01-02 12:28:00', NULL, 'devisTransformé', NULL, 'Moule', 'Moule', 276, 11, NULL, 'CPT2023DEC00254', 276, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(255, 249, 1, 2, NULL, NULL, '2023-12-13 19:30:21', '2024-01-02 12:29:00', '2024-01-10 12:29:00', NULL, 'devisTransformé', NULL, 'Moule', 'Moule', 585, 8, NULL, 'CPT2023DEC00255', 585, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(256, 250, 18, 2, NULL, NULL, '2023-12-16 04:23:06', '2024-05-15 15:00:00', '2024-05-30 04:24:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 0, 15, NULL, 'CPT2023DEC00256', 0, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(257, 206, 12, 2, NULL, NULL, '2023-12-16 04:26:57', '2024-05-01 15:00:00', '2024-05-30 04:23:46', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 559, 29, NULL, 'CPT2023DEC00257', 559, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(258, 156, 15, 2, NULL, NULL, '2023-12-24 04:14:30', '2024-01-02 21:13:00', '2024-01-15 21:13:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 50, 13, NULL, 'CPT2023DEC00258', 0, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(260, 251, 15, 2, NULL, NULL, '2023-12-24 15:52:18', '2024-01-20 08:46:00', '2024-03-05 08:46:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 50, 45, NULL, 'CPT2023DEC00260', 0, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(261, 160, 17, 2, NULL, NULL, '2023-12-24 16:29:39', '2024-07-24 11:30:00', '2024-08-04 11:30:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 0, 11, NULL, 'CPT2023DEC00261', 0, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(262, 160, 18, 2, NULL, NULL, '2023-12-24 16:31:31', '2024-08-10 23:30:00', '2024-08-17 23:00:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 0, 7, NULL, 'CPT2023DEC00262', 0, 0, 0, NULL, 0, 1, 0, 0, NULL, NULL, NULL, NULL),
(263, 252, 17, 2, NULL, NULL, '2023-12-25 17:12:43', '2024-01-24 10:10:00', '2024-02-07 10:10:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 50, 14, NULL, 'CPT2023DEC00263', 0, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(264, 209, 3, 2, NULL, NULL, '2023-12-28 16:15:55', '2024-03-16 09:12:00', '2024-03-31 09:13:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 585, 15, NULL, 'CPT2023DEC00264', 375, 0, 160, NULL, 0, 1, 0, 1, NULL, NULL, NULL, NULL),
(265, 240, 17, 2, NULL, NULL, '2024-01-02 17:25:15', '2023-12-29 10:24:00', '2024-01-16 10:24:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 0, 18, NULL, 'CPT2024JAN00265', 0, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(266, 253, 12, 2, NULL, NULL, '2024-01-05 03:54:14', '2024-01-02 12:00:00', '2024-01-08 20:50:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 220, 6, NULL, 'CPT2024JAN00266', 220, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(267, 150, 1, 2, NULL, NULL, '2024-01-06 20:14:26', '2024-01-14 08:00:00', '2024-01-23 11:00:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 585, 9, NULL, 'CPT2024JAN00267', 585, 0, 0, NULL, 0, 1, 0, 0, NULL, NULL, NULL, NULL),
(268, 254, 11, 2, NULL, NULL, '2024-01-07 08:33:45', '2024-01-07 01:35:00', '2024-01-15 01:30:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 469, 8, NULL, 'CPT2024JAN00268', 469, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(269, 209, 11, 2, NULL, NULL, '2024-01-08 16:22:47', '2024-03-16 09:20:00', '2024-03-31 09:20:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 585, 15, NULL, 'CPT2024JAN00269', 375, 0, 160, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(270, 255, 12, 2, NULL, NULL, '2024-01-09 02:47:59', '2024-01-09 19:42:00', '2024-02-08 04:51:23', NULL, 'devisTransformé', NULL, 'Moule', 'Moule', 930, 30, NULL, 'CPT2024JAN00270', 930, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(271, 166, 12, 2, NULL, NULL, '2024-01-09 23:56:04', '2024-03-09 20:51:00', '2024-03-19 16:51:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 425, 10, NULL, 'CPT2024JAN00271', 375, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(272, 256, 18, 2, NULL, NULL, '2024-01-10 21:52:41', '2024-01-25 14:49:00', '2024-02-06 14:50:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 578, 12, NULL, 'CPT2024JAN00272', 528, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(273, 232, 18, 2, NULL, NULL, '2024-01-13 00:34:00', '2024-01-12 17:35:00', '2024-01-22 14:00:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Aéroport de Point-à-pitre', 0, 10, NULL, 'CPT2024JAN00273', 0, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(274, 150, 11, 2, NULL, NULL, '2024-01-16 02:48:11', '2024-01-15 20:46:00', '2024-01-19 01:46:00', NULL, 'devisTransformé', NULL, 'Morne-à-l\'Eau', 'Morne-à-l\'Eau', 270, 4, NULL, 'CPT2024JAN00274', 220, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(275, 254, 17, 2, NULL, NULL, '2024-01-17 02:06:43', '2024-01-16 20:05:00', '2024-01-18 19:05:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 60, 2, NULL, 'CPT2024JAN00275', 60, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(276, 150, 1, 2, NULL, NULL, '2024-01-18 02:27:37', '2024-01-19 19:26:00', '2024-01-24 01:26:00', NULL, 'devisTransformé', NULL, 'Morne-à-l\'Eau', 'Morne-à-l\'Eau', 292, 5, NULL, 'CPT2024JAN00276', 292, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(277, 150, 2, 2, NULL, NULL, '2024-01-22 04:38:59', '2024-01-24 21:37:00', '2024-01-28 00:37:00', NULL, 'devisTransformé', NULL, 'Morne-à-l\'Eau', 'Morne-à-l\'Eau', 252, 4, NULL, 'CPT2024JAN00277', 202, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(278, 257, 2, 2, NULL, NULL, '2024-01-26 03:36:41', '2024-04-13 20:30:00', '2024-04-28 20:30:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 207.3, 15, NULL, 'CPT2024JAN00278', 207.3, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(279, 4, 13, NULL, NULL, 'PANNE MOTEUR', '2024-01-26 03:45:36', '2024-01-26 20:44:00', '2024-05-30 20:44:00', NULL, 'stopSale', 'GARAGE FAZER', 'garage', NULL, NULL, 125, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, NULL),
(280, 4, 16, NULL, NULL, 'ACCIDENT ARR', '2024-01-27 16:33:33', '2024-02-01 09:31:00', '2024-02-16 09:32:00', NULL, 'stopSale', 'COFFRE LUNETTE ARR', 'garage', NULL, NULL, 15, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, NULL),
(281, 150, 1, 2, NULL, NULL, '2024-01-28 04:03:51', '2024-02-04 21:01:00', '2024-02-29 21:02:00', NULL, 'devisTransformé', NULL, 'Morne-à-l\'Eau', 'Morne-à-l\'Eau', 1220, 25, NULL, 'CPT2024JAN00281', 1170, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(282, 258, 2, 2, NULL, NULL, '2024-01-28 04:43:03', '2024-02-04 21:38:00', '2024-02-29 21:38:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 425, 25, NULL, 'CPT2024JAN00282', 425, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(283, 160, 18, 2, NULL, NULL, '2024-01-30 19:11:42', '2024-02-24 18:30:00', '2024-03-02 12:10:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 0, 7, NULL, 'CPT2024JAN00283', 0, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(284, 162, 8, 2, NULL, NULL, '2024-01-31 18:55:09', '2024-03-14 11:53:00', '2024-06-08 13:56:38', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 450, 86, NULL, 'CPT2024JAN00284', 450, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(285, 259, 10, 2, NULL, NULL, '2024-02-07 04:58:59', '2024-07-19 03:53:00', '2024-08-13 23:07:10', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 750, 25, NULL, 'CPT2024FEV00285', 750, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(286, 260, 11, 2, NULL, NULL, '2024-02-09 04:31:26', '2024-03-11 21:28:00', '2024-03-16 00:31:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 188, 5, NULL, 'CPT2024FEV00286', 188, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(287, 4, 12, NULL, NULL, 'PARCHOC AVANT', '2024-02-10 01:09:58', '2024-02-09 21:08:00', '2024-03-08 18:09:00', NULL, 'stopSale', 'CARROSERIE', 'garage', NULL, NULL, 28, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, NULL),
(288, 261, 2, 2, NULL, NULL, '2024-02-11 00:45:36', '2024-03-06 17:42:00', '2024-04-03 17:42:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 450, 28, NULL, 'CPT2024FEV00288', 450, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(289, 262, 11, 2, NULL, NULL, '2024-02-16 21:00:45', '2024-02-20 13:56:00', '2024-02-24 13:56:00', NULL, 'devisTransformé', NULL, 'Gare Maritime de Bergervin', 'Gare Maritime de Bergervin', 196, 4, NULL, 'CPT2024FEV00289', 196, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(290, 263, 18, 2, NULL, NULL, '2024-02-20 01:53:17', '2024-02-19 18:52:00', '2024-02-22 18:49:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 90, 3, NULL, 'CPT2024FEV00290', 90, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(291, 156, 15, 2, NULL, NULL, '2024-02-22 02:12:22', '2024-03-05 19:10:00', '2024-03-18 19:10:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 0, 13, NULL, 'CPT2024FEV00291', 0, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(292, 264, 8, 2, NULL, NULL, '2024-02-23 02:02:49', '2024-02-22 19:59:00', '2024-02-25 18:59:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 83.7, 3, NULL, 'CPT2024FEV00292', 83.7, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(293, 265, 17, 2, NULL, NULL, '2024-02-27 21:31:31', '2024-03-22 14:27:00', '2024-04-01 14:27:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 0, 10, NULL, 'CPT2024FEV00293', 0, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(294, 254, 11, 2, NULL, NULL, '2024-02-28 01:00:02', '2024-02-27 18:59:00', '2024-03-04 17:59:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 196, 6, NULL, 'CPT2024FEV00294', 196, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(295, 266, 18, 2, NULL, NULL, '2024-02-29 21:07:39', '2024-03-18 14:02:00', '2024-03-21 14:02:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 0, 3, NULL, 'CPT2024FEV00295', 0, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(296, 267, 1, 2, NULL, NULL, '2024-03-01 19:44:45', '2024-03-07 12:41:00', '2024-04-02 12:41:00', NULL, 'devisTransformé', NULL, 'Moule', 'Moule', 1170, 26, NULL, 'CPT2024MAR00296', 1170, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(297, 268, 11, 2, NULL, NULL, '2024-03-01 22:19:40', '2024-04-05 15:13:00', '2024-04-19 15:13:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 275, 14, NULL, 'CPT2024MAR00297', 275, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(298, 184, 11, 2, NULL, NULL, '2024-03-03 23:34:34', '2024-05-02 16:33:00', '2024-06-17 16:33:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 900, 46, NULL, 'CPT2024MAR00298', 900, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(299, 214, 12, 2, NULL, NULL, '2024-03-05 14:33:22', '2024-07-31 07:31:00', '2024-08-18 07:31:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 750, 18, NULL, 'CPT2024MAR00299', 750, 0, 0, NULL, 0, 1, 0, 0, NULL, NULL, NULL, NULL),
(300, 236, 11, 2, NULL, NULL, '2024-03-06 01:48:49', '2024-03-06 18:47:00', '2024-03-11 18:47:00', NULL, 'devisTransformé', NULL, 'Moule', 'Moule', 110, 5, NULL, 'CPT2024MAR00300', 110, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(301, 269, 17, 2, NULL, NULL, '2024-03-13 22:17:29', '2024-03-13 17:13:00', '2024-03-17 15:13:00', NULL, 'devisTransformé', NULL, 'Sainte-anne', 'Sainte-anne', 132, 4, NULL, 'CPT2024MAR00301', 132, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(302, 156, 15, 2, NULL, NULL, '2024-03-16 21:54:12', '2024-03-18 20:58:00', '2024-03-25 14:52:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 50, 7, NULL, 'CPT2024MAR00302', 0, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(303, 271, 18, 2, NULL, NULL, '2024-03-16 22:29:10', '2024-03-29 15:24:00', '2024-04-01 15:24:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 0, 3, NULL, 'CPT2024MAR00303', 0, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(304, 266, 18, 2, NULL, NULL, '2024-03-19 03:48:25', '2024-03-24 20:47:00', '2024-03-26 20:47:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 0, 2, NULL, 'CPT2024MAR00304', 0, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(305, 272, 1, 2, NULL, NULL, '2024-03-22 20:26:31', '2024-04-19 13:20:00', '2024-04-29 13:20:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 350, 10, NULL, 'CPT2024MAR00305', 350, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(306, 225, 12, 2, NULL, NULL, '2024-03-22 20:31:02', '2024-03-22 13:36:00', '2024-03-24 13:30:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 75, 2, NULL, 'CPT2024MAR00306', 75, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(307, 225, 12, 2, NULL, NULL, '2024-03-30 02:49:57', '2024-03-30 19:49:00', '2024-04-06 19:49:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 188, 7, NULL, 'CPT2024MAR00307', 188, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(308, 273, 15, 2, NULL, NULL, '2024-03-30 20:24:28', '2024-03-30 14:22:00', '2024-04-01 13:22:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 70, 2, NULL, 'CPT2024MAR00308', 70, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(309, 274, 11, 2, NULL, NULL, '2024-03-30 21:08:16', '2024-07-06 18:03:00', '2024-07-27 18:03:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 750, 21, NULL, 'CPT2024MAR00309', 750, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(310, 190, 12, 2, NULL, NULL, '2024-04-01 17:09:26', '2024-04-07 10:04:00', '2024-04-30 10:05:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 551, 23, NULL, 'CPT2024AVR00310', 551, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(311, 190, 10, 2, NULL, NULL, '2024-04-01 17:17:39', '2024-04-30 10:14:00', '2024-06-30 10:15:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 551, 61, NULL, 'CPT2024AVR00311', 551, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(312, 271, 18, 2, NULL, NULL, '2024-04-02 00:47:25', '2024-08-02 09:46:00', '2024-08-10 09:46:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 0, 8, NULL, 'CPT2024AVR00312', 0, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(313, 275, 1, 2, NULL, NULL, '2024-04-02 03:54:08', '2024-04-03 20:51:00', '2024-04-14 20:51:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 250, 11, NULL, 'CPT2024AVR00313', 250, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(314, 276, 17, 2, NULL, NULL, '2024-04-02 16:30:54', '2024-04-02 15:24:00', '2024-04-13 09:24:00', NULL, 'devisTransformé', NULL, 'Moule', 'Moule', 0, 11, NULL, 'CPT2024AVR00314', 0, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(315, 277, 2, 2, NULL, NULL, '2024-04-05 04:32:08', '2024-07-31 21:28:00', '2024-08-13 22:28:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 519.5, 13, NULL, 'CPT2024AVR00315', 519.5, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(316, 278, 10, 2, NULL, NULL, '2024-04-06 21:48:06', '2024-04-13 14:42:00', '2024-04-26 14:42:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 259.96, 13, NULL, 'CPT2024AVR00316', 259.96, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(317, 279, 18, 2, NULL, NULL, '2024-04-12 17:58:29', '2024-04-12 10:37:00', '2024-04-21 10:56:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Aéroport de Point-à-pitre', 0, 9, NULL, 'CPT2024AVR00317', 0, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(318, 167, 17, 2, NULL, NULL, '2024-04-15 23:47:02', '2024-04-15 17:45:00', '2024-06-30 16:45:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 0, 76, NULL, 'CPT2024AVR00318', 0, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(319, 259, 17, 2, NULL, NULL, '2024-04-15 23:49:22', '2024-04-16 16:47:00', '2024-04-23 16:47:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 259.99, 7, NULL, 'CPT2024AVR00319', 259.99, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(320, 280, 11, 2, NULL, NULL, '2024-04-16 18:45:48', '2024-07-29 15:28:00', '2024-08-01 15:29:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 125, 3, NULL, 'CPT2024AVR00320', 75, 0, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(321, 280, 12, 2, NULL, NULL, '2024-04-16 18:47:12', '2024-07-16 15:46:00', '2024-08-01 15:46:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 750, 16, NULL, 'CPT2024AVR00321', 750, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(322, 156, 12, 2, NULL, NULL, '2024-04-22 20:34:42', '2024-06-05 13:33:00', '2024-06-19 13:33:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 224, 14, NULL, 'CPT2024AVR00322', 224, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(323, 281, 11, 2, NULL, NULL, '2024-04-23 00:44:39', '2024-04-22 18:41:00', '2024-05-01 17:42:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 275, 9, NULL, 'CPT2024AVR00323', 275, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(324, 225, 17, 2, NULL, NULL, '2024-04-23 23:58:05', '2024-04-23 17:00:00', '2024-05-14 16:57:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Aéroport de Point-à-pitre', 0, 21, NULL, 'CPT2024AVR00324', 0, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(325, 282, 2, 2, NULL, NULL, '2024-05-02 04:46:43', '2024-05-01 21:49:00', '2024-05-10 03:44:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 165, 9, NULL, 'CPT2024MAI00325', 165, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(327, 283, 1, 2, NULL, NULL, '2024-05-08 04:53:11', '2024-05-02 21:51:00', '2024-05-17 21:51:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 465, 15, NULL, 'CPT2024MAI00327', 465, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(328, 282, 2, 2, NULL, NULL, '2024-05-08 04:56:50', '2024-05-10 22:01:00', '2024-05-19 23:06:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 165, 9, NULL, 'CPT2024MAI00328', 165, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(329, 254, 18, 2, NULL, NULL, '2024-05-08 05:09:30', '2024-05-08 22:09:00', '2024-05-11 22:09:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 0, 3, NULL, 'CPT2024MAI00329', 0, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(330, 283, 17, 2, NULL, NULL, '2024-05-18 03:45:29', '2024-05-17 21:44:00', '2024-05-20 20:44:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 50, 3, NULL, 'CPT2024MAI00330', 0, 50, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(331, 283, 17, 2, NULL, NULL, '2024-05-28 18:46:29', '2024-05-28 13:25:00', '2024-05-30 13:25:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 60, 2, NULL, 'CPT2024MAI00331', 60, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(332, 149, 12, 2, NULL, NULL, '2024-06-06 22:53:00', '2024-08-09 19:51:00', '2024-09-09 19:51:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 750, 31, NULL, 'CPT2024JUI00332', 750, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(333, 160, 18, 2, NULL, NULL, '2024-06-06 23:01:41', '2024-06-07 19:53:00', '2024-06-14 19:54:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 0, 7, NULL, 'CPT2024JUI00333', 0, 0, 0, NULL, 0, 1, 0, 0, NULL, NULL, NULL, NULL),
(334, 162, 8, 2, NULL, NULL, '2024-06-06 23:14:28', '2024-06-30 16:13:00', '2024-07-30 16:13:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 330, 30, NULL, 'CPT2024JUI00334', 330, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(335, 180, 3, 2, NULL, NULL, '2024-06-07 00:08:45', '2024-06-08 13:07:00', '2024-06-16 13:08:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 274, 8, NULL, 'CPT2024JUI00335', 224, 50, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(336, 215, 18, 2, NULL, NULL, '2024-06-08 00:35:01', '2024-06-07 20:33:00', '2024-06-12 17:34:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 0, 5, NULL, 'CPT2024JUI00336', 0, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(337, 162, 2, 2, NULL, NULL, '2024-06-08 14:02:27', '2024-06-08 10:01:00', '2024-06-13 07:01:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 82, 5, NULL, 'CPT2024JUI00337', 82, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(338, 225, 8, 2, NULL, NULL, '2024-06-08 14:06:46', '2024-06-08 20:05:00', '2024-06-15 07:05:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 82, 7, NULL, 'CPT2024JUI00338', 82, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(339, 180, 10, 2, NULL, NULL, '2024-06-11 03:57:39', '2024-06-16 20:56:00', '2024-06-25 20:56:00', NULL, 'devisTransformé', NULL, 'Moule', 'Moule', 274, 9, NULL, 'CPT2024JUI00339', 224, 50, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(340, 284, 11, 2, NULL, NULL, '2024-06-28 05:15:00', '2024-08-14 10:11:00', '2024-08-20 18:42:51', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 375, 6, NULL, 'CPT2024JUI00340', 375, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(341, 261, 1, 2, NULL, NULL, '2024-06-28 06:27:42', '2024-08-01 00:26:00', '2024-08-15 00:27:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 565, 14, NULL, 'CPT2024JUI00341', 565, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(342, 285, 17, 2, NULL, NULL, '2024-07-05 00:09:01', '2024-07-05 04:05:00', '2024-07-11 01:05:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 0, 6, NULL, 'CPT2024JUIL00342', 0, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(343, 167, 18, 2, NULL, NULL, '2024-07-05 00:14:58', '2024-07-05 00:13:00', '2024-07-11 21:13:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 0, 6, NULL, 'CPT2024JUIL00343', 0, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(344, 4, 1, NULL, NULL, 'PANNE MOTEUR', '2024-07-05 00:17:31', '2024-07-01 17:16:00', '2024-07-21 17:17:00', NULL, 'stopSale', 'durite eau', 'garage', NULL, NULL, 20, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, NULL),
(346, 286, 18, 2, NULL, NULL, '2024-07-10 17:44:48', '2024-07-22 06:41:00', '2024-08-02 06:41:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 0, 11, NULL, 'CPT2024JUIL00346', 0, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(347, 287, 15, 2, NULL, NULL, '2024-07-11 16:12:53', '2024-07-23 13:10:00', '2024-08-11 13:10:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 0, 19, NULL, 'CPT2024JUIL00347', 0, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(348, 167, 2, 2, NULL, NULL, '2024-07-18 23:41:34', '2024-07-18 17:38:00', '2024-07-29 16:38:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 304, 11, NULL, 'CPT2024JUIL00348', 304, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(349, 288, 16, 2, NULL, NULL, '2024-07-19 13:34:30', '2024-07-19 08:31:00', '2024-07-31 06:31:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 700, 12, NULL, 'CPT2024JUIL00349', 700, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(350, 285, 17, 2, NULL, NULL, '2024-07-20 00:56:10', '2024-07-19 20:55:00', '2024-07-23 17:55:00', NULL, 'devisTransformé', NULL, 'Abymes', 'Abymes', 210, 4, NULL, 'CPT2024JUIL00350', 210, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(351, 4, 13, NULL, NULL, 'PANNE MOTEUR', '2024-07-20 01:41:43', '2024-07-19 20:41:00', '2024-08-07 18:41:00', NULL, 'stopSale', 'CASSE', 'garage', NULL, NULL, 19, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, NULL),
(352, 162, 18, 2, NULL, NULL, '2024-07-20 01:50:39', '2024-11-01 18:47:00', '2024-11-30 18:47:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 250, 29, NULL, 'CPT2024JUIL00352', 200, 50, 0, NULL, 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(353, 259, 16, 2, NULL, NULL, '2024-07-22 23:14:36', '2024-07-31 16:12:00', '2024-09-04 16:13:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 0, 35, NULL, 'CPT2024JUIL00353', 0, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(354, 160, 17, 2, NULL, NULL, '2024-07-23 00:27:37', '2024-08-10 17:26:00', '2024-08-17 17:26:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 0, 7, NULL, 'CPT2024JUIL00354', 0, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(355, 289, 11, 2, NULL, NULL, '2024-08-13 04:16:42', '2025-03-01 21:09:00', '2025-03-09 21:10:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 375, 8, NULL, 'CPT2024AOU00355', 375, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(356, 167, 10, 2, NULL, NULL, '2024-08-13 23:08:41', '2024-08-14 16:07:00', '2024-09-14 16:07:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 450, 31, NULL, 'CPT2024AOU00356', 450, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(357, 290, 8, 2, NULL, NULL, '2024-08-17 22:42:54', '2025-01-15 15:39:00', '2025-02-13 15:40:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 660, 29, NULL, 'CPT2024AOU00357', 660, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(358, 291, 2, 2, NULL, NULL, '2024-08-20 18:35:37', '2024-10-16 11:30:00', '2024-12-15 11:31:00', NULL, 'devisTransformé', NULL, 'Saint-François', 'Saint-François', 432, 60, NULL, 'CPT2024AOU00358', 432, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(359, 176, 17, 2, NULL, NULL, '2024-08-20 18:37:34', '2024-08-20 22:36:00', '2024-08-31 19:36:00', NULL, 'devisTransformé', NULL, 'Pointe-à-pitre', 'Pointe-à-pitre', 0, 11, NULL, 'CPT2024AOU00359', 0, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(360, 284, 18, 2, NULL, NULL, '2024-08-20 18:44:19', '2024-08-20 12:43:00', '2024-08-28 11:43:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 0, 8, NULL, 'CPT2024AOU00360', 0, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(361, 277, 11, 2, NULL, NULL, '2024-08-20 18:47:55', '2024-08-21 05:46:00', '2024-08-28 19:47:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 187.5, 7, NULL, 'CPT2024AOU00361', 187.5, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(362, 215, 11, 2, NULL, NULL, '2024-08-22 19:58:10', '2024-08-31 16:57:00', '2024-09-05 16:57:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Aéroport de Point-à-pitre', 187.5, 5, NULL, 'CPT2024AOU00362', 187.5, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(363, 184, 18, 2, NULL, NULL, '2024-08-29 13:44:27', '2024-09-26 12:41:00', '2024-10-01 06:41:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 210, 5, NULL, 'CPT2024AOU00363', 210, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(364, 167, 11, 2, NULL, NULL, '2024-09-07 05:43:26', '2024-09-07 12:14:00', '2024-09-24 11:42:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 306, 17, NULL, 'CPT2024SEP00364', 306, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(365, 292, 12, 2, NULL, NULL, '2024-09-07 06:13:55', '2025-02-14 23:06:00', '2025-02-26 23:07:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 360, 12, NULL, 'CPT2024SEP00365', 360, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(366, 159, 11, 2, NULL, NULL, '2024-09-07 13:54:03', '2025-03-10 06:52:00', '2025-03-31 06:52:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 910, 21, NULL, 'CPT2024SEP00366', 750, 0, 160, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(367, 167, 11, 2, NULL, NULL, '2024-09-18 13:58:17', '2024-09-24 23:56:00', '2024-09-30 06:56:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 108, 6, NULL, 'CPT2024SEP00367', 108, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(368, 183, 10, 2, NULL, NULL, '2024-09-18 14:10:32', '2024-09-18 07:12:00', '2024-10-14 07:09:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 450, 26, NULL, 'CPT2024SEP00368', 450, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(369, 293, 15, 2, NULL, NULL, '2024-09-24 01:00:17', '2024-09-23 18:15:00', '2024-09-30 17:30:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 210, 7, NULL, 'CPT2024SEP00369', 210, 0, 0, NULL, 0, 1, 0, 0, NULL, NULL, NULL, NULL),
(370, 167, 11, 2, NULL, NULL, '2024-09-24 06:21:48', '2024-10-01 07:19:00', '2024-10-03 07:19:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 70, 2, NULL, 'CPT2024SEP00370', 70, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(371, 294, 8, 2, NULL, NULL, '2024-09-25 03:19:50', '2025-02-16 20:14:00', '2025-03-13 20:15:00', NULL, 'devisTransformé', NULL, 'Gare Maritime de Bergervin', 'Gare Maritime de Bergervin', 425.05, 25, NULL, 'CPT2024SEP00371', 425.05, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(372, 225, 1, 2, NULL, NULL, '2024-09-26 13:47:58', '2024-09-26 07:01:00', '2024-09-30 10:44:00', NULL, 'devisTransformé', NULL, 'Moule', 'Moule', 80, 4, NULL, 'CPT2024SEP00372', 80, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(373, 296, 15, 2, NULL, NULL, '2024-09-26 23:30:12', '2024-09-26 16:30:00', '2024-09-29 16:30:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 75, 3, NULL, 'CPT2024SEP00373', 75, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(375, 297, 16, 2, NULL, NULL, '2024-09-27 18:52:54', '2024-09-27 11:55:00', '2024-09-29 11:50:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 60, 2, NULL, 'CPT2024SEP00374', 60, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(376, 298, 2, 2, NULL, NULL, '2024-09-28 14:50:37', '2024-09-28 07:50:00', '2024-09-30 07:00:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 30, 2, NULL, 'CPT2024SEP00376', 30, 0, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(381, 295, 11, 3, NULL, NULL, '2024-10-07 21:03:31', '2024-11-03 08:00:00', '2024-11-10 17:17:00', NULL, 'devisTransformé', NULL, 'Agence du Moule', 'Agence du Moule', 130, 7, '178', 'WEB2024OCT00377', 159, 0, 0, 'null', 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(382, 284, 13, 3, NULL, NULL, '2024-10-08 18:14:20', '2025-01-07 14:27:00', '2025-01-20 14:27:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 530, 13, '176', 'WEB2024OCT00382', 585, 0, 0, 'null', 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(383, 180, 2, 2, NULL, NULL, '2024-10-08 18:43:57', '2024-01-02 18:42:00', '2024-01-31 18:43:00', NULL, 'devisTransformé', NULL, 'Gosier', 'Gosier', 950, 29, 'DV2300170', 'CPT2024OCT00383', 900, 0, 0, 'null', 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(384, 117, 1, 2, NULL, NULL, '2024-10-13 11:15:57', '2024-10-14 12:00:00', '2024-10-18 12:00:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 1070, 4, 'DV2400179', 'CPT2024OCT00384', 500, 220, 350, 'null', 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(385, 117, 17, 2, NULL, NULL, '2024-11-28 20:18:20', '2024-11-28 19:16:00', '2024-12-08 19:16:00', NULL, 'devisTransformé', NULL, 'Gare Maritime de Bergervin', 'Aéroport de Point-à-pitre', 710, 10, 'DV2400187', 'CPT2024NOV00385', 20, 340, 350, 'null', 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(386, 117, 18, 2, NULL, NULL, '2024-12-09 19:19:01', '2024-12-10 12:00:00', '2024-12-12 12:00:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 280, 2, 'DV2400189', 'CPT2024DEC00386', 100, 80, 100, 'null', 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(387, 117, 18, 2, NULL, NULL, '2024-12-09 19:19:15', '2024-12-10 12:00:00', '2024-12-12 12:00:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 280, 2, 'DV2400189', 'CPT2024DEC00387', 100, 80, 100, 'null', 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(388, 117, 17, 2, NULL, NULL, '2024-12-10 21:35:36', '2024-12-11 12:00:00', '2024-12-12 23:00:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 290, 1, 'DV2400191', 'CPT2024DEC00388', 100, 30, 160, 'null', 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(389, 117, 18, 2, NULL, NULL, '2024-12-22 12:52:35', '2024-12-23 12:48:00', '2024-12-29 12:48:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 650, 6, 'DV2400196', 'CPT2024DEC00389', 210, 90, 350, 'null', 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(390, 117, 18, 2, NULL, NULL, '2024-12-22 12:57:06', '2024-12-23 12:00:00', '2024-12-31 12:00:00', NULL, 'devisTransformé', NULL, 'Moule', 'Port-louis', 890, 8, 'DV2400195', 'CPT2024DEC00390', 450, 90, 350, 'null', 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(391, 117, 18, 2, NULL, NULL, '2024-12-23 09:38:49', '2024-12-23 12:00:00', '2024-12-24 12:00:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 220, 1, 'DV2400192', 'CPT2024DEC00391', 100, 120, 0, 'null', 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(392, 117, 18, 2, NULL, NULL, '2024-12-23 09:39:12', '2024-12-23 12:00:00', '2024-12-24 12:00:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 220, 1, 'DV2400192', 'CPT2024DEC00392', 100, 120, 0, 'null', 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(393, 117, 17, 2, NULL, NULL, '2024-12-23 11:14:20', '2024-12-25 10:27:00', '2024-12-28 10:27:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 740, 3, 'DV2400200', 'CPT2024DEC00393', 100, 290, 350, 'null', 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(394, 117, 16, 2, NULL, NULL, '2024-12-23 20:39:51', '2024-12-24 12:00:00', '2024-12-26 12:00:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 350, 2, NULL, 'CPT2024DEC00394', 100, 150, 100, 'null', 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(395, 117, 16, 2, NULL, NULL, '2024-12-23 20:45:51', '2024-12-24 12:00:00', '2024-12-26 12:00:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 350, 2, NULL, 'CPT2024DEC00395', 100, 150, 100, 'null', 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(396, 117, 16, 2, NULL, NULL, '2024-12-23 20:46:42', '2024-12-24 12:00:00', '2024-12-26 12:00:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 350, 2, NULL, 'CPT2024DEC00396', 100, 150, 100, 'null', 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(397, 117, 16, 2, NULL, NULL, '2024-12-23 20:59:50', '2024-12-24 12:00:00', '2024-12-26 12:00:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 410, 2, NULL, 'CPT2024DEC00397', 100, 210, 100, 'null', 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(398, 117, 16, 2, NULL, NULL, '2024-12-23 21:02:33', '2024-12-27 21:01:00', '2025-01-05 21:02:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 760, 9, 'DV2400202', 'CPT2024DEC00398', 450, 120, 190, 'null', 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(399, 117, 17, 2, NULL, NULL, '2024-12-29 19:46:22', '2024-12-30 12:00:00', '2025-01-05 12:00:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 645, 6, NULL, 'CPT2024DEC00399', 210, 345, 90, 'null', 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(400, 117, 18, 2, NULL, NULL, '2025-04-15 20:39:30', '2025-04-16 12:00:00', '2025-04-30 13:00:00', NULL, 'devisTransformé', NULL, 'Morne-à-l\'Eau', 'Petit-canal', 570, 14, 'DV2500215', 'CPT2025AVR00400', 450, 120, 0, '8VE62156AJ255792Y', 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(401, 117, 18, 2, NULL, NULL, '2025-04-15 20:43:13', '2025-04-16 12:00:00', '2025-04-30 13:00:00', NULL, 'devisTransformé', NULL, 'Morne-à-l\'Eau', 'Petit-canal', 570, 14, 'DV2500215', 'CPT2025AVR00401', 450, 120, 0, '0NR44180Y91134748', 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(402, 117, 18, 2, NULL, NULL, '2025-04-15 20:43:55', '2025-04-16 12:00:00', '2025-04-30 13:00:00', NULL, 'devisTransformé', NULL, 'Morne-à-l\'Eau', 'Petit-canal', 570, 14, 'DV2500215', 'CPT2025AVR00402', 450, 120, 0, '2TR256649D261722E', 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(403, 117, 18, 2, NULL, NULL, '2025-04-15 20:46:08', '2025-04-16 12:00:00', '2025-04-30 13:00:00', NULL, 'devisTransformé', NULL, 'Morne-à-l\'Eau', 'Petit-canal', 570, 14, 'DV2500215', 'CPT2025AVR00403', 450, 120, 0, '4PL974727U506345D', 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(404, 117, 18, 2, NULL, NULL, '2025-04-15 20:49:17', '2025-04-16 12:00:00', '2025-04-30 13:00:00', NULL, 'devisTransformé', NULL, 'Morne-à-l\'Eau', 'Petit-canal', 570, 14, 'DV2500215', 'CPT2025AVR00404', 450, 120, 0, '4J42987971509661V', 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(405, 117, 18, 2, NULL, NULL, '2025-04-15 20:51:17', '2025-04-16 12:00:00', '2025-04-30 13:00:00', NULL, 'devisTransformé', NULL, 'Morne-à-l\'Eau', 'Petit-canal', 570, 14, 'DV2500215', 'CPT2025AVR00405', 450, 120, 0, '0XV580021D697553E', 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(406, 117, 18, 2, NULL, NULL, '2025-04-15 20:53:14', '2025-04-16 12:00:00', '2025-04-30 13:00:00', NULL, 'devisTransformé', NULL, 'Morne-à-l\'Eau', 'Petit-canal', 570, 14, 'DV2500215', 'CPT2025AVR00406', 450, 120, 0, '9PF42113F1350150J', 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(407, 117, 18, 2, NULL, NULL, '2025-04-15 20:54:37', '2025-04-16 12:00:00', '2025-04-30 13:00:00', NULL, 'devisTransformé', NULL, 'Morne-à-l\'Eau', 'Petit-canal', 570, 14, 'DV2500215', 'CPT2025AVR00407', 450, 120, 0, '0LK60657AJ571103C', 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(408, 117, 17, 2, NULL, NULL, '2025-04-17 17:46:53', '2025-04-18 12:00:00', '2025-04-20 12:00:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 190, 2, 'DV2500216', 'CPT2025AVR00408', 100, 90, 0, '9W1035544K694903B', 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(409, 117, 16, 2, NULL, NULL, '2025-04-17 17:58:36', '2025-04-18 14:00:00', '2025-04-30 14:00:00', NULL, 'devisTransformé', NULL, 'Morne-à-l\'Eau', 'Port-louis', 540, 12, 'DV2500217', 'CPT2025AVR00409', 450, 0, 90, '1DF669967K111551W', 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(410, 117, 16, 2, NULL, NULL, '2025-04-17 17:59:46', '2025-04-18 14:00:00', '2025-04-30 14:00:00', NULL, 'devisTransformé', NULL, 'Morne-à-l\'Eau', 'Port-louis', 540, 12, 'DV2500217', 'CPT2025AVR00410', 450, 0, 90, '2CM2527188868773T', 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(411, 117, 16, 2, NULL, NULL, '2025-04-17 18:00:57', '2025-04-18 14:00:00', '2025-04-30 14:00:00', NULL, 'devisTransformé', NULL, 'Morne-à-l\'Eau', 'Port-louis', 540, 12, 'DV2500217', 'CPT2025AVR00411', 450, 0, 90, '4NM02837NV2159426', 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(412, 117, 16, 2, NULL, NULL, '2025-04-17 18:05:58', '2025-04-18 14:00:00', '2025-04-30 14:00:00', NULL, 'devisTransformé', NULL, 'Morne-à-l\'Eau', 'Port-louis', 540, 12, 'DV2500217', 'CPT2025AVR00412', 450, 0, 90, '62T55226PT2367946', 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(413, 117, 17, 2, NULL, NULL, '2025-04-20 14:47:25', '2025-04-23 12:00:00', '2025-05-02 12:00:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 450, 9, '*********', 'CPT2025AVR00413', 450, 0, 0, '2PH10266EL759854J', 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(414, 117, 15, 2, NULL, NULL, '2025-04-20 16:23:53', '2025-04-21 12:00:00', '2025-04-29 13:00:00', NULL, 'devisTransformé', NULL, 'Morne-à-l\'Eau', 'Aéroport de Point-à-pitre', 450, 8, '*********', 'CPT2025AVR00414', 450, 0, 0, '37U08168BC787872J', 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(415, 117, 13, 2, NULL, NULL, '2025-04-21 16:02:31', '2025-04-22 12:00:00', '2025-04-30 12:00:00', NULL, 'devisTransformé', NULL, 'Morne-à-l\'Eau', 'Aéroport de Point-à-pitre', 554, 8, NULL, 'CPT2025AVR00415', 554, 0, 0, 'null', 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(416, 117, 12, 2, NULL, NULL, '2025-04-21 18:58:06', '2025-04-22 12:00:00', '2025-04-30 12:00:00', NULL, 'devisTransformé', NULL, 'Moule', 'Pointe-à-pitre', 275, 8, 'DV2500227', 'CPT2025AVR00416', 275, 0, 0, 'null', 0, 0, 0, 0, NULL, NULL, NULL, NULL);
INSERT INTO `reservation` (`id`, `client_id`, `vehicule_id`, `mode_reservation_id`, `etat_reservation_id`, `type`, `date_reservation`, `date_debut`, `date_fin`, `lieu`, `code_reservation`, `commentaire`, `agence_depart`, `agence_retour`, `prix`, `duree`, `num_devis`, `reference`, `tarif_vehicule`, `prix_options`, `prix_garanties`, `stripe_session_id`, `archived`, `canceled`, `reported`, `conducteur`, `saisisseur_km_id`, `km_depart`, `km_retour`, `date_km`) VALUES
(417, 373, 18, 2, NULL, NULL, '2025-05-09 14:28:14', '2025-05-13 12:00:00', '2025-05-29 12:00:00', NULL, 'devisTransformé', NULL, 'Moule', 'Port-louis', 925, 16, 'DV2500232', 'CPT2025MAI00417', 800, 125, 0, '52606439UX590650F', 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(418, 375, 17, 2, NULL, NULL, '2025-05-12 19:26:57', '2025-05-17 12:00:00', '2025-05-19 12:00:00', NULL, 'devisTransformé', NULL, 'Gosier', 'Sainte-anne', 100, 2, 'DV2500233', 'CPT2025MAI00418', 100, 0, 0, '8SN64339PD1649737', 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(419, 117, 17, 2, NULL, NULL, '2025-05-20 18:17:27', '2025-05-21 12:00:00', '2025-05-29 12:03:00', NULL, 'devisTransformé', NULL, 'Morne-à-l\'Eau', 'Aéroport de Point-à-pitre', 720, 8, 'DV2500234', 'CPT2025MAI00419', 450, 170, 100, '58E671956H660694Y', 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(420, 117, 18, 2, NULL, NULL, '2025-06-24 18:40:07', '2025-06-25 18:36:00', '2025-06-30 18:36:00', NULL, 'devisTransformé', NULL, 'Moule', 'Sainte-anne', 875, 5, 'DV2500240', 'CPT2025JUI00420', 210, 315, 350, '9WP84900H2760332J', 0, 0, 0, 1, NULL, NULL, NULL, NULL),
(421, 117, 18, 2, NULL, NULL, '2025-07-03 20:43:37', '2025-07-31 20:41:00', '2025-08-30 20:41:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 920, 30, 'DV2500245', 'CPT2025JUIL00421', 800, 120, 0, '2FS22725P3329451R', 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(422, 117, 18, 2, NULL, NULL, '2025-07-10 20:10:43', '2025-07-10 19:17:00', '2025-07-17 19:17:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 300, 7, 'DV2500246', 'CPT2025JUIL00422', 210, 90, 0, '04893931NB9995141', 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(423, 117, 18, 2, NULL, NULL, '2025-07-10 20:55:21', '2025-07-10 19:17:00', '2025-07-17 19:17:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 300, 7, 'DV2500246', 'CPT2025JUIL00423', 210, 90, 0, 'TEST_PAYMENT_686ffe89c0694', 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(424, 117, 18, 2, NULL, NULL, '2025-07-10 20:56:25', '2025-07-10 19:17:00', '2025-07-17 19:17:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 300, 7, 'DV2500246', 'CPT2025JUIL00424', 210, 90, 0, 'TEST_PAYMENT_686ffec95d82f', 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(425, 117, 17, 2, NULL, NULL, '2025-07-10 21:00:17', '2025-07-10 20:59:00', '2025-07-11 20:59:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 130, 1, 'DV2500247', 'CPT2025JUIL00425', 100, 30, 0, '4DH33375R3739192Y', 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(426, 117, 17, 2, NULL, NULL, '2025-07-10 21:00:49', '2025-07-10 20:59:00', '2025-07-11 20:59:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 130, 1, 'DV2500247', 'CPT2025JUIL00426', 100, 30, 0, '2U303132CV7312936', 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(427, 117, 16, 2, NULL, NULL, '2025-07-10 21:12:32', '2025-07-10 21:11:00', '2025-07-11 21:11:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 100, 1, 'DV2500248', 'CPT2025JUIL00427', 100, 0, 0, '2M056642PY135662T', 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(428, 117, 15, 2, NULL, NULL, '2025-07-10 21:21:29', '2025-07-10 21:20:00', '2025-07-11 21:20:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 100, 1, 'DV2500249', 'CPT2025JUIL00428', 100, 0, 0, '7YF35218KG740863M', 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(429, 117, 17, 2, NULL, NULL, '2025-07-16 06:56:33', '2025-07-16 06:53:00', '2025-07-17 06:53:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 160, 1, NULL, 'CPT2025JUIL00429', 100, 60, 0, 'null', 0, 0, 0, 0, NULL, NULL, NULL, NULL),
(430, 117, 18, 2, NULL, NULL, '2025-07-17 19:38:42', '2025-07-18 19:22:00', '2025-07-24 19:22:00', NULL, 'devisTransformé', NULL, 'Aéroport de Point-à-pitre', 'Aéroport de Point-à-pitre', 270, 6, 'DV2500250', 'CPT2025JUIL00430', 210, 60, 0, 'null', 0, 0, 0, 0, NULL, NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Structure de la table `reservation_conducteur`
--

DROP TABLE IF EXISTS `reservation_conducteur`;
CREATE TABLE IF NOT EXISTS `reservation_conducteur` (
  `reservation_id` int NOT NULL,
  `conducteur_id` int NOT NULL,
  PRIMARY KEY (`reservation_id`,`conducteur_id`),
  KEY `IDX_43CDB8F7B83297E7` (`reservation_id`),
  KEY `IDX_43CDB8F7F16F4AC6` (`conducteur_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `reservation_conducteur`
--

INSERT INTO `reservation_conducteur` (`reservation_id`, `conducteur_id`) VALUES
(23, 45),
(63, 38),
(78, 37),
(96, 39),
(104, 40),
(235, 45),
(251, 41),
(251, 42),
(399, 45);

-- --------------------------------------------------------

--
-- Structure de la table `reservation_garantie`
--

DROP TABLE IF EXISTS `reservation_garantie`;
CREATE TABLE IF NOT EXISTS `reservation_garantie` (
  `reservation_id` int NOT NULL,
  `garantie_id` int NOT NULL,
  PRIMARY KEY (`reservation_id`,`garantie_id`),
  KEY `IDX_EC26243CB83297E7` (`reservation_id`),
  KEY `IDX_EC26243CA4B9602F` (`garantie_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `reservation_garantie`
--

INSERT INTO `reservation_garantie` (`reservation_id`, `garantie_id`) VALUES
(20, 5),
(21, 4),
(22, 4),
(22, 5),
(23, 5),
(24, 4),
(26, 4),
(27, 5),
(29, 5),
(43, 6),
(47, 5),
(50, 4),
(97, 6),
(98, 6),
(152, 6),
(264, 6),
(269, 6),
(366, 6),
(384, 4),
(384, 5),
(384, 6),
(385, 4),
(385, 5),
(385, 6),
(386, 5),
(387, 5),
(388, 6),
(389, 4),
(389, 5),
(389, 6),
(390, 4),
(390, 5),
(390, 6),
(393, 4),
(393, 5),
(393, 6),
(394, 5),
(395, 5),
(396, 5),
(397, 5),
(398, 4),
(398, 5),
(399, 4),
(409, 4),
(410, 4),
(411, 4),
(412, 4),
(419, 5),
(420, 4),
(420, 5),
(420, 6);

-- --------------------------------------------------------

--
-- Structure de la table `reservation_options`
--

DROP TABLE IF EXISTS `reservation_options`;
CREATE TABLE IF NOT EXISTS `reservation_options` (
  `reservation_id` int NOT NULL,
  `options_id` int NOT NULL,
  PRIMARY KEY (`reservation_id`,`options_id`),
  KEY `IDX_B7A04102B83297E7` (`reservation_id`),
  KEY `IDX_B7A041023ADB05F1` (`options_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `reservation_options`
--

INSERT INTO `reservation_options` (`reservation_id`, `options_id`) VALUES
(20, 2),
(21, 2),
(22, 2),
(22, 3),
(23, 3),
(24, 2),
(26, 4),
(27, 2),
(29, 5),
(31, 2),
(47, 4),
(61, 4),
(63, 2),
(63, 5),
(63, 6),
(72, 2),
(74, 2),
(384, 2),
(384, 3),
(384, 4),
(384, 5),
(384, 6),
(385, 2),
(385, 3),
(385, 4),
(385, 5),
(385, 6),
(386, 2),
(387, 2),
(388, 3),
(390, 4),
(390, 5),
(390, 6);

-- --------------------------------------------------------

--
-- Structure de la table `reset_password`
--

DROP TABLE IF EXISTS `reset_password`;
CREATE TABLE IF NOT EXISTS `reset_password` (
  `id` int NOT NULL AUTO_INCREMENT,
  `token` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` datetime NOT NULL,
  `user_id` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_B9983CE5A76ED395` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `reset_password`
--

INSERT INTO `reset_password` (`id`, `token`, `created_at`, `user_id`) VALUES
(1, '614b0fae9b55a', '2021-09-22 14:12:46', NULL),
(2, '614b10245c6bd', '2021-09-22 14:14:44', NULL),
(3, '614b1434653a6', '2021-09-22 14:32:04', NULL),
(4, '614b169180e9c', '2021-09-22 14:42:09', NULL),
(5, '614b173e9b094', '2021-09-22 14:45:02', NULL),
(6, '614b1df7a467a', '2021-09-22 15:13:43', NULL),
(7, '614b20805b4bb', '2021-09-22 15:24:32', NULL),
(8, '615559bbcbeec', '2021-09-30 09:31:23', NULL),
(9, '61fd6a6ee3eb9', '2022-02-04 21:03:26', NULL),
(10, '6242a3088b47a', '2022-03-29 09:11:20', 4),
(11, '6242a4748e875', '2022-03-29 09:17:24', 4),
(12, '62f3c66bdb8a5', '2022-08-10 17:53:31', 164),
(13, '66cb35bea9bdd', '2024-08-25 16:46:38', 164),
(14, '66cb361e8fafe', '2024-08-25 16:48:14', 117),
(15, '67e2dbbdcbdf4', '2025-03-25 19:37:17', 117),
(16, '67e2dbccba9a3', '2025-03-25 19:37:32', 117),
(17, '67e2dbdb9d0f2', '2025-03-25 19:37:47', 117),
(18, '67e2dbea613bc', '2025-03-25 19:38:02', 117),
(19, '6820e7eb6f534', '2025-05-11 21:09:47', 117),
(20, '68222b5da3513', '2025-05-12 20:09:49', 376);

-- --------------------------------------------------------

--
-- Structure de la table `tarifs`
--

DROP TABLE IF EXISTS `tarifs`;
CREATE TABLE IF NOT EXISTS `tarifs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `trois_jours` double DEFAULT NULL,
  `sept_jours` double DEFAULT NULL,
  `mois` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `quinze_jours` double DEFAULT NULL,
  `trente_jours` double DEFAULT NULL,
  `marque_id` int DEFAULT NULL,
  `modele_id` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_F9B8C4964827B9B2` (`marque_id`),
  KEY `IDX_F9B8C496AC14B70A` (`modele_id`)
) ENGINE=InnoDB AUTO_INCREMENT=136 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `tarifs`
--

INSERT INTO `tarifs` (`id`, `trois_jours`, `sept_jours`, `mois`, `quinze_jours`, `trente_jours`, `marque_id`, `modele_id`) VALUES
(88, 97, 220, 'Janvier', 469, 930, 1, 1),
(89, 85, 196, 'Février', 420, 837, 1, 1),
(90, 75, 188, 'Mars', 375, 750, 1, 1),
(91, 60, 137, 'Avril', 275, 551, 1, 1),
(92, 120, 292, 'Janvier', 585, 1170, 1, 2),
(93, 120, 292, 'Février', 585, 1170, 1, 2),
(94, 120, 292, 'Mars', 585, 1170, 1, 2),
(95, 111, 277, 'Avril', 554, 1108, 1, 2),
(96, 81, 202, 'Janvier', 405, 810, 1, 3),
(97, 83.7, 182.5, 'Février', 364.5, 729, 1, 3),
(98, 75, 112.5, 'Mars', 225, 450, 1, 3),
(99, 54.9, 108.7, 'Avril', 217.5, 435, 1, 3),
(100, 45, 112.5, 'Mai', 224.5, 449, 1, 1),
(101, 93, 232.5, 'Mai', 465, 930, 1, 2),
(102, 33, 82.5, 'Mai', 165, 330, 1, 3),
(103, 45, 112.5, 'Juin', 224, 449, 1, 1),
(104, 93, 232, 'Juin', 465, 930, 1, 2),
(105, 33, 82, 'Juin', 165, 330, 1, 3),
(106, 75, 187.5, 'Juillet', 375, 750, 1, 1),
(107, 108, 274.5, 'Juillet', 549, 1098, 1, 2),
(108, 60, 152, 'Juillet', 304, 608, 1, 3),
(109, 75, 187.5, 'Août', 375, 750, 1, 1),
(110, 60, 152, 'Août', 304, 608, 1, 3),
(111, 114, 282, 'Août', 565, 1130, 1, 2),
(112, 70, 167.2, 'Septembre', 334.5, 669, 1, 1),
(113, 100, 247.5, 'Septembre', 495, 990, 1, 2),
(114, 100, 247.5, 'Octobre', 495, 990, 1, 2),
(115, 100, 247, 'Novembre', 495, 990, 1, 2),
(116, 105, 262.8, 'Décembre', 525.5, 1051, 1, 2),
(117, 35, 88, 'Septembre', 175, 350, 1, 3),
(118, 70, 167.2, 'Octobre', 334.5, 669, 1, 1),
(119, 43.5, 108, 'Octobre', 216, 432, 1, 3),
(120, 63, 159, 'Novembre', 319, 638, 1, 1),
(121, 48, 120, 'Novembre', 240, 480, 1, 3),
(122, 78, 194.5, 'Décembre', 389, 778, 1, 1),
(123, 57, 138, 'Décembre', 276, 552, 1, 3),
(124, 100, 210, 'Janvier', 450, 800, 1, 10),
(125, 100, 210, 'Février', 450, 800, 1, 10),
(126, 100, 210, 'Mars', 450, 800, 1, 10),
(127, 100, 210, 'Avril', 450, 800, 1, 10),
(128, 100, 210, 'Mai', 450, 800, 1, 10),
(129, 100, 210, 'Juin', 450, 800, 1, 10),
(130, 100, 210, 'Juillet', 450, 800, 1, 10),
(131, 100, 210, 'Août', 450, 800, 1, 10),
(132, 100, 210, 'Septembre', 450, 800, 1, 10),
(133, 100, 210, 'Octobre', 450, 800, 1, 10),
(134, 100, 210, 'Novembre', 450, 800, 1, 10),
(135, 100, 210, 'Décembre', 450, 800, 1, 10);

-- --------------------------------------------------------

--
-- Structure de la table `test`
--

DROP TABLE IF EXISTS `test`;
CREATE TABLE IF NOT EXISTS `test` (
  `id` int NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `type`
--

DROP TABLE IF EXISTS `type`;
CREATE TABLE IF NOT EXISTS `type` (
  `id` int NOT NULL AUTO_INCREMENT,
  `libelle` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `type`
--

INSERT INTO `type` (`id`, `libelle`) VALUES
(1, 'BERLINE');

-- --------------------------------------------------------

--
-- Structure de la table `user`
--

DROP TABLE IF EXISTS `user`;
CREATE TABLE IF NOT EXISTS `user` (
  `id` int NOT NULL AUTO_INCREMENT,
  `roles` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '(DC2Type:json)',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `nom` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `prenom` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `adresse` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `mail` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `telephone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `portable` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `presence` tinyint(1) NOT NULL,
  `date_inscription` datetime NOT NULL,
  `fonction` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `recupass` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `date_naissance` date DEFAULT NULL,
  `numero_permis` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `date_permis` date DEFAULT NULL,
  `lieu_naissance` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `complement_adresse` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `ville` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `code_postal` double DEFAULT NULL,
  `ville_delivrance_permis` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `username` varchar(180) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `infos_resa_id` int DEFAULT NULL,
  `infos_vol_resa_id` int DEFAULT NULL,
  `sexe` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UNIQ_8D93D6495126AC48` (`mail`),
  UNIQUE KEY `UNIQ_8D93D6491DF27D6E` (`infos_resa_id`),
  UNIQUE KEY `UNIQ_8D93D649B62F3B9C` (`infos_vol_resa_id`)
) ENGINE=InnoDB AUTO_INCREMENT=379 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `user`
--

INSERT INTO `user` (`id`, `roles`, `password`, `nom`, `prenom`, `adresse`, `mail`, `telephone`, `portable`, `presence`, `date_inscription`, `fonction`, `recupass`, `date_naissance`, `numero_permis`, `date_permis`, `lieu_naissance`, `complement_adresse`, `ville`, `code_postal`, `ville_delivrance_permis`, `username`, `infos_resa_id`, `infos_vol_resa_id`, `sexe`) VALUES
(4, '[\"ROLE_SUPER_ADMIN\"]', '$2y$13$iAop8cGiP2/0.sH5C8jDVOjHSOJDcs29q0k5hhfshVQsBz7oLAz/.', 'administrateur', 'admin', 'admin', '<EMAIL>', 'admin', 'admin', 1, '2021-02-17 00:00:00', 'ROLE_SUPER_ADMIN', '$2y$13$Ia8D1GGU6RY7nTepbhR9eOKB6UqG9Mmb7jMPQZ0YNtdbZbPODxM9a', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '', NULL, NULL, NULL),
(117, '[\"ROLE_CLIENT\"]', '$2y$13$BzeAI/RdMsMCFDzLJwyhnuhWAYXR1mdBwScKSxIqTBTuZA3TD1sZW', 'rakotoarinelina', 'Rija no', 'test', '<EMAIL>', '034 78 765 48', '0325072183', 1, '2021-07-26 00:00:00', NULL, '$argon2id$v=19$m=65536,t=4,p=1$TVdiVnl1T3dIbzEyZ3FXQg$YB7RTn96d3ZOvAwvlqIhJNHXp+BkYTgiR7OKUgZ4jeQ', '1991-07-27', '431301356', '2021-08-09', 'Antsirabe', NULL, 'test', 101, 'Antananarivo', '', 2, 2, 'masculin'),
(145, '[\"ROLE_CLIENT\"]', '$argon2id$v=19$m=65536,t=4,p=1$QUJaYzRMVU9hM1RXaXJaUA$pFbLIXrV9qZJm2h25H1PGzBePw4+ykgBfMcv8oN2BQ0', 'MANDRET', 'MYLENE', NULL, '<EMAIL>', '0637377088', NULL, 1, '2022-02-02 00:00:00', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'MANDRET', NULL, NULL, NULL),
(146, '[\"ROLE_CLIENT\"]', '$argon2id$v=19$m=65536,t=4,p=1$bDIzb2RpdThJOGMxWE1Ncw$Ej/hzv/ej6CIhoy/uqbSP13MgzBYEfz3xECrVQxa0B4', 'LUKOKI', 'MURICE', '84 rue luisbunnel', '<EMAIL>', '0662184118', '0662184118', 1, '2022-02-02 00:00:00', NULL, NULL, NULL, NULL, NULL, NULL, '84 rue luisbunnel', 'maux', 77100, NULL, 'Mandret', 7, 7, NULL),
(147, '[\"ROLE_CLIENT\"]', '$2y$13$GDa.2KqnSSbICPuL01mPCuEomFo3HlDVDq8QjaK2JAxCxUkYmKOEW', 'Saula', 'elvicia', '178 SE SCAMA', '<EMAIL>', '+261325970360', '+261325970360', 1, '2022-02-02 00:00:00', NULL, NULL, '1993-12-07', '123456', NULL, 'Antsiranana', 'DS', 'Antsiranana', 201, 'Diégo', 'Saula', 4, 4, NULL),
(148, '[\"ROLE_CLIENT\"]', '$argon2id$v=19$m=65536,t=4,p=1$Mi5HNDVtR0R6VDZ2U3VQRA$TzULlh6XHq1gQBeoAwYKy6UUY/d6LxHpRr5Mt3rxryI', 'Berneval', 'Marie', NULL, '<EMAIL>', '06 21 84 63 75', NULL, 1, '2022-02-20 00:00:00', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Berneval', NULL, NULL, NULL),
(149, '[\"ROLE_CLIENT\"]', '$argon2id$v=19$m=65536,t=4,p=1$N1o5cFE2TTFBY1ZISGoxRA$U6T95TQyWn17LCEuIds3onL5nZLjEuwbDBIVBZzw/vk', 'DE ALMEIDA', 'MARIO', 'ghjklmù', '<EMAIL>', '0690737674', '0515665', 1, '2022-03-20 23:58:47', NULL, NULL, NULL, NULL, NULL, NULL, 'uiop', 'gju', 545, NULL, 'DE ALMEIDA', 12, 12, NULL),
(150, '[\"ROLE_CLIENT\"]', '$argon2id$v=19$m=65536,t=4,p=1$anpJRHZEU0Z6UElQckNmRw$eKGdpr0ZlqPHecBJT6Y/huMvELKRe+rgTX+sEHxS+Fo', 'montauban', 'adelaide nicole', NULL, '<EMAIL>', '0618535840', NULL, 1, '2022-03-21 00:03:35', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'montauban', NULL, NULL, NULL),
(151, '[\"ROLE_CLIENT\"]', '$argon2id$v=19$m=65536,t=4,p=1$bFF3MEJ1akxQUjBUdkR4Yg$x7PqxLF/qJLmZlwBCuoIbhi6pE8XRlZhfYRdVVeeY7A', 'calazel', 'barbara nadia', NULL, '<EMAIL>', '0770102974', NULL, 1, '2022-03-21 00:08:03', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'calazel', NULL, NULL, NULL),
(152, '[\"ROLE_CLIENT\"]', '$argon2id$v=19$m=65536,t=4,p=1$UlRCV3d1cTBrTnphV3FsZQ$9aPDPRrgRDABIST+1db+hYCfrPbZFkxXGuafLbGhcug', 'stephan', 'leo', NULL, '<EMAIL>', '0768932969', NULL, 1, '2022-03-21 00:12:12', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'stephan', NULL, NULL, NULL),
(153, '[\"ROLE_CLIENT\"]', '$argon2id$v=19$m=65536,t=4,p=1$NUc4Z2V6S20wbEcyQXg4Wg$V4v0XyAlrGGETObA5Xh6axZt+4dJI5HFeU26hZI1vxs', 'bois', 'eric emmanuel', NULL, '<EMAIL>', '0667234192', NULL, 1, '2022-03-21 00:16:21', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'bois', NULL, NULL, NULL),
(154, '[\"ROLE_CLIENT\"]', '$argon2id$v=19$m=65536,t=4,p=1$WlNmTjhvakpTV3F3OEQvMA$9UUezlowcPDYCiJmoVj0Nioqf+V1CBpWgxEBkWOyaeg', 'piserchia', 'rosina colette nicolina', NULL, '<EMAIL>', '0590859680', NULL, 1, '2022-03-21 00:20:58', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'piserchia', NULL, NULL, NULL),
(155, '[\"ROLE_CLIENT\"]', '$argon2id$v=19$m=65536,t=4,p=1$WUxpWDVIU2ZKakdPUmk5Zw$6jX6V4VZC0rEOjfsEGZ0OS1eZwmd5knn6k1Yv3mnqdk', 'kumar', 'sophie francine', NULL, '<EMAIL>', '0770071717', NULL, 1, '2022-03-21 00:24:25', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'kumar', NULL, NULL, NULL),
(156, '[\"ROLE_CLIENT\"]', '$argon2id$v=19$m=65536,t=4,p=1$bVhRZHNaV0FHUnVNbjlHbg$PWf1I8oj+SiLPVI2hHwsEnYzAiXe7WaQcti+1TD1TH4', 'housni', 'mohammed', NULL, '<EMAIL>', '0613174942', NULL, 1, '2022-03-21 00:37:32', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'housni', NULL, NULL, NULL),
(157, '[\"ROLE_CLIENT\"]', '$argon2id$v=19$m=65536,t=4,p=1$LlA2OHIwODRIOGdNRXNGSg$i8uEqjkEAOsWzqbcU6ZPZC45TrC502/Hgj5+d2dIFt4', 'marre', 'antoinette', NULL, '<EMAIL>', '0689451818', NULL, 1, '2022-03-21 01:06:15', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'marre', NULL, NULL, NULL),
(158, '[\"ROLE_CLIENT\"]', '$argon2id$v=19$m=65536,t=4,p=1$QlcwbTBQb0J4U2F4dEdPUg$mlum/AsOj2m218EG+NyRXdVpLZHmIh2zQOKwkczJUIw', 'foulquier', 'laure', NULL, '<EMAIL>', '0630', NULL, 1, '2022-03-21 01:12:16', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'foulquier', NULL, NULL, NULL),
(159, '[\"ROLE_CLIENT\"]', '$argon2id$v=19$m=65536,t=4,p=1$SGdnSHVleUtadmw5TlBzSg$+dnBBlhm10FW+h4dIwTRHm7pRdr0REGWtqedKycZv44', 'SOMVILLE', 'MARGAUX', NULL, '<EMAIL>', '+32499229014', NULL, 1, '2022-03-22 01:58:58', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'SOMVILLE', NULL, NULL, NULL),
(160, '[\"ROLE_CLIENT\"]', '$argon2id$v=19$m=65536,t=4,p=1$TUtBLnExMkdPWktIY2JnOQ$OvGh9aWuyqNbZonfnbtM+cBDhCLkRErRa0LDQhIE7d4', 'accajou', 'ken', '6 ALLE DE LA TETE A L4 ANGLAIS', '<EMAIL>', '0609635168', '0609635168', 1, '2022-03-22 02:21:23', NULL, NULL, NULL, NULL, NULL, NULL, 'LOT SEZE', 'ST FRANCOIS', 97118, NULL, 'accajou', 8, 8, NULL),
(161, '[\"ROLE_CLIENT\"]', '$argon2id$v=19$m=65536,t=4,p=1$dFE4bldvSVdjd3E5OC9qcQ$choEdXi4lVW43x5NKk6P/GyzE52WoUWKGC3kQVzbbIw', 'carpentier', 'Thibault pierre Michel', NULL, '<EMAIL>', '0674126685', NULL, 1, '2022-03-28 04:35:34', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'carpentier', NULL, NULL, NULL),
(162, '[\"ROLE_CLIENT\"]', '$argon2id$v=19$m=65536,t=4,p=1$ZlcyS2ZSZlFRY2ZOUEVOdA$dl9AnX6U6tK8mgyAKbzEEzygFUpJWSuCYGVMwGZ61ZE', 'VELAIDON', 'FRANCIANNE', NULL, '<EMAIL>', '0636978350', NULL, 1, '2022-03-29 05:43:17', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'VELAIDON', NULL, NULL, NULL),
(164, '[\"ROLE_SUPER_ADMIN\"]', '$2y$13$ezO9lKbNI7oiKrscTJvHBOP05057k0O/Ztr/9cgudY8huB9WnO836', 'Raberia', NULL, 'Antananarivo', '<EMAIL>', '032', '034', 1, '2022-03-31 11:40:01', 'Administrateur', '$2y$13$Hx/6dZbmhUrBco.pTxZQOeBcK0UjgBK9x8X0jNap6oCQlZqqXtRg6', '2022-03-09', '43130', '2022-03-16', 'Antsirabe', 'test', 'Tana', 101, 'Antananarivo', 'Blade', NULL, NULL, NULL),
(165, '[\"ROLE_PERSONNEL\"]', '$2y$13$arFsUHH287kxBiyCmqJo9.dcj6qTchmQMUZg0aAcx5172q1pVPRj.', 'RAKOTOARINELINA', 'Rija', 'Ambatomainty', '<EMAIL>', '0325072183', '0347876548', 1, '2022-04-05 09:42:34', 'ROLE_PERSONNEL', '$2y$13$arFsUHH287kxBiyCmqJo9.dcj6qTchmQMUZg0aAcx5172q1pVPRj.', '2022-04-08', '43130', '2022-04-09', 'Antsirabe', 'test', 'Antananarivo', NULL, NULL, NULL, NULL, NULL, NULL),
(166, '[\"ROLE_CLIENT\"]', '$2y$13$7iK5IyzzLAo/zHKwLmvRfOCmqkn04ZKqtyor0Wu7f5oIRFhG3kiHe', 'LE GARGASSON', 'Jean Claude', NULL, '<EMAIL>', '0682420225', NULL, 1, '2022-05-29 04:02:27', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'LE GARGASSON', NULL, NULL, NULL),
(167, '[\"ROLE_CLIENT\"]', '$2y$13$sa5Dex727sD8iaCGzb74l.H/rvqc4/Qk.omuwkSxN4qcjrHOmAK9C', 'SEYMOUR KANCEL', 'Sylvie Apolline', NULL, '<EMAIL>', '0650113233', NULL, 1, '2022-05-29 04:15:28', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'SEYMOUR KANCEL', NULL, NULL, NULL),
(168, '[\"ROLE_CLIENT\"]', '$2y$13$CZAJ/OTg86V0mQGgtJ/8eeSUoDiEcVg3ygRyXQLSYjsQCW.AfpeDa', 'THEBO', 'Joel', NULL, '<EMAIL>', '0610012925', NULL, 1, '2022-05-29 04:40:48', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'THEBO', NULL, NULL, NULL),
(169, '[\"ROLE_CLIENT\"]', '$2y$13$J0XY6wKY68jVy/MxiTZCjO2f85VEEg/XXq2CVgB/syMRfUwC2eRym', 'ROUQUIN', 'Cédric Ulrich', NULL, '<EMAIL>', '+33677603449', NULL, 1, '2022-06-06 17:55:35', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'ROUQUIN', NULL, NULL, NULL),
(170, '[\"ROLE_CLIENT\"]', '$2y$13$uxUe2VZW8rvThJs60tEDAOaqi9St.EBlbXYHbFF65yqeh04PCta2O', 'DUGUET', 'NATHALIE', NULL, '<EMAIL>', '0685708193', NULL, 1, '2022-06-08 22:38:51', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'DUGUET', NULL, NULL, NULL),
(171, '[\"ROLE_CLIENT\"]', '$2y$13$numgWPsIfA3ua4.bM02tQu20sfvWs01KZKlEb1fHEBkxbE8Xkd0tW', 'Guannel', 'Caroline', '26 Rue des SARRAZINS', '<EMAIL>', '00000000', '0000000', 1, '2022-06-10 05:34:56', NULL, NULL, NULL, NULL, NULL, NULL, '-', 'CRETEIL', 94000, NULL, 'PAYGAMBAR', 3, 3, NULL),
(172, '[\"ROLE_CLIENT\"]', '$2y$13$sgFDqsIP6JYZeanj4stvzO2pEic2IPSU69t6BGG9i/pNay2H5lZ1a', 'Petitfrere', 'Stephanie', '41 AVENUE DES VIGNES', '<EMAIL>', '+33684138122', '+33684138122', 1, '2022-06-20 20:15:54', NULL, NULL, NULL, NULL, NULL, NULL, '.', 'CHAMIGNY', 77260, NULL, 'Petitfrere', 5, 5, NULL),
(173, '[\"ROLE_CLIENT\"]', '$2y$13$aaarIV3STMrzhrfwLdIiPe4ZdkMQw4pi7BGX5/srsYzLGIWAPNai6', 'Popotte', 'Olga Annick', NULL, '<EMAIL>', '+33673971767', NULL, 1, '2022-06-21 15:44:49', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Popotte', NULL, NULL, NULL),
(174, '[\"ROLE_CLIENT\"]', '$2y$13$kFKklsn52lt9n7N7Eof7Oehwu0e2PPwxPzH/uolCWoM3nx97/pOGG', 'madinska', 'jimmy', NULL, '<EMAIL>', '0690717980', NULL, 1, '2022-07-06 17:02:36', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'madinska', NULL, NULL, NULL),
(175, '[\"ROLE_CLIENT\"]', '$2y$13$KcYfL1bl2pWBWDZcxPR.3uDhneRL9n57qidiCu1RGYKPZ5r2vPMYi', 'JACOBIN', 'JOHAN SAMUEL', NULL, '<EMAIL>', '+33669724795', NULL, 1, '2022-07-07 03:45:48', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'JACOBIN', NULL, NULL, NULL),
(176, '[\"ROLE_CLIENT\"]', '$2y$13$mynd0OecDGDN6M5fXjL42eAdPduqr9DtnpXENx6bbvmImB5laNMXy', 'DIARA', 'Karine', NULL, '<EMAIL>', '+************', NULL, 1, '2022-07-12 00:11:34', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'DIARA', NULL, NULL, NULL),
(177, '[\"ROLE_CLIENT\"]', '$2y$13$.z9ECtQo6ogYmZKIbDA0T.j8/5V7SnpMrIWD3NE2MPGvj3hVevfMC', 'GUIDICELLI MOLE', 'AURELIE', NULL, '<EMAIL>', '0643955019', NULL, 1, '2022-07-21 17:23:18', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'GUIDICELLI MOLE', NULL, NULL, NULL),
(178, '[\"ROLE_CLIENT\"]', '$2y$13$4cXQQPru/bDVrkqDBI1yUOhv5/BXeeogB0C8HvHcHEPzluDqJ46zC', 'ORSONI', 'JEAN BAPTISTE', NULL, '<EMAIL>', '0643955019', NULL, 1, '2022-07-21 18:25:32', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'ORSONI', NULL, NULL, NULL),
(179, '[\"ROLE_CLIENT\"]', '$2y$13$KtLY0Yj0BOBUMS7N4jlcRekiWL1yQDvt0GyoFbSI3zuSgfLOlepfO', 'Poulet', 'Franck', '426 RUE DU BOIS', '<EMAIL>', '0', '0', 1, '2022-08-03 02:45:30', NULL, NULL, NULL, NULL, NULL, NULL, '59178 BOUSIGNIES', 'BOUSIGNIES', 59178, NULL, 'Poulet', 6, 6, NULL),
(180, '[\"ROLE_CLIENT\"]', '$2y$13$D0GGRiBpLOSJ4FG4hAeVoOVa9ZqeVEKq.Iy2XEgZBa37h3VgOXas.', 'Mandret', 'Joel', NULL, '<EMAIL>', '+************', NULL, 1, '2022-08-07 05:54:30', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Mandret', NULL, NULL, NULL),
(181, '[\"ROLE_CLIENT\"]', '$2y$13$Eu1gq90I.LWoAzqN4m6SI.W5qyWnSe7HLgVRvGwtY3R/42qABGTre', 'SOLVET', 'YVANNAH', NULL, '<EMAIL>', '0765706615', NULL, 1, '2022-08-09 16:51:53', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'SOLVET', NULL, NULL, NULL),
(182, '[\"ROLE_CLIENT\"]', '$2y$13$n60WU22bZSBjIz9AFt6HQOK3d17.ssrPVmlP9ZEBrdBCAMIkbnFrS', 'Simon', 'Martin', '162 rue Fontaine St-Jean-sur-Richelieu', '<EMAIL>', '+1 514 654-3606', '+1 514 654-3606', 1, '2022-08-10 00:06:13', NULL, NULL, NULL, NULL, NULL, NULL, 'Canada', 'Québec', 218, NULL, 'Simon', 9, 9, NULL),
(183, '[\"ROLE_CLIENT\"]', '$2y$13$NEFGx.li88XEk6vUYeGq9uOLrFPSGW7NWQGIYXtsG4REpYwTEy4eW', 'Kancel', 'Amélie', NULL, '<EMAIL>', '+33650113233', NULL, 1, '2022-08-10 00:20:52', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Kancel', NULL, NULL, NULL),
(184, '[\"ROLE_CLIENT\"]', '$2y$13$eHHqoTNrUYO8caqs8yyThuIHzUW64QiYIH5m55jvUbweQ4kfLV8Gi', 'Patoor', 'Alain', NULL, '<EMAIL>', '0684540590', NULL, 1, '2022-08-17 23:54:38', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Patoor', NULL, NULL, NULL),
(185, '[\"ROLE_CLIENT\"]', '$2y$13$ZZ.mmDrGxhbyXiLuTN7NCuNu/yayavTiliVVbkPSakgyd4huYzIRa', 'PAUL', 'ERMIONE AUDREY', NULL, '<EMAIL>', '0767681992', NULL, 1, '2022-08-25 01:32:22', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'PAUL', NULL, NULL, NULL),
(186, '[\"ROLE_CLIENT\"]', '$2y$13$n.mUR9NneGN52mjwc2RWOOoOzQZeEVGEP85kNOSAnNUrOu8D9pbEK', 'FRANCILLETTE', 'BRUNO', NULL, '<EMAIL>', '+33777732831', NULL, 1, '2022-09-02 19:00:41', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'FRANCILLETTE', NULL, NULL, NULL),
(187, '[\"ROLE_CLIENT\"]', '$2y$13$/YQZqQlN4zHn1PtO1mUWve4BS9sp2iWOQlwguHqsQBkzzR1qNv/qS', 'DUGAMIN', 'CONTANCE MOENA', '6 ALLE DE LA TETE A L\' ANGLAIS', '<EMAIL>', '0690718396', '0690718396', 1, '2022-09-02 22:37:26', NULL, NULL, NULL, NULL, NULL, NULL, '.', 'ST FRANCOIS', 97118, NULL, 'DUGAMIN', 10, 10, NULL),
(188, '[\"ROLE_CLIENT\"]', '$2y$13$72IhiNQPjp0Xx1zR1oRqhuWgMb/0BqbrEKbpeUg5Tg1B2WHKiKXdO', 'moyer', 'amboise rosan', NULL, '<EMAIL>', '+33664767859', NULL, 1, '2022-09-08 15:48:26', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'moyer', NULL, NULL, NULL),
(189, '[\"ROLE_CLIENT\"]', '$2y$13$3oh.xRIWk6tJmBL/Ouw44O3MO5alEJ.3l3aYdvGGQxCNh/Oad6HH2', 'melloul', 'richard', NULL, '<EMAIL>', '0690648304', NULL, 1, '2022-09-11 23:32:38', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'melloul', NULL, NULL, NULL),
(190, '[\"ROLE_CLIENT\"]', '$2y$13$1xjmqWWeEdlNYn9CpnMzhuNrdFw5Z.b6YOb.0hsxDpZq051FTBWvi', 'LEBORNE', 'RAPHAELLA NICOLE', NULL, '<EMAIL>', '0690526941', NULL, 1, '2022-09-12 15:38:43', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'LEBORNE', NULL, NULL, NULL),
(191, '[\"ROLE_CLIENT\"]', '$2y$13$1psgOAWvzpZdoy6MC/xY9uA2xuFsKYul1htUT2pXYRq/zEA163YE.', 'LENOX', 'CONSTANT SERGE', NULL, '<EMAIL>', '07 87 08 26 83', NULL, 1, '2022-10-06 02:28:55', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'LENOX', NULL, NULL, NULL),
(192, '[\"ROLE_CLIENT\"]', '$2y$13$NNsJyTYmYau8kydwTqqVf.rzMpYjH5x9TNIYWHncAVeyOvtrWdlpG', 'Sylvie', 'Césaire', NULL, '<EMAIL>', '0690 34 23 20', NULL, 1, '2022-10-21 20:05:58', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Sylvie', NULL, NULL, NULL),
(193, '[\"ROLE_CLIENT\"]', '$2y$13$tSEONTo7Cd6ixMJtAltTT.oQfM4GyYuNrWgBMmaxEhquIDU52D8vC', 'ALONSO', 'MAXIME ROMAIN', NULL, '<EMAIL>', '0699287279', NULL, 1, '2022-10-26 22:17:50', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'ALONSO', NULL, NULL, NULL),
(194, '[\"ROLE_CLIENT\"]', '$2y$13$we9KizRKuBe4uVXhTvmYPuoh1O1YRIiq5j5h1HQyQmk1Wl15uc1Du', 'rubir', 'jean pierre henri', NULL, '<EMAIL>', 'JP RUBIR', NULL, 1, '2022-11-08 13:35:32', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'rubir', NULL, NULL, NULL),
(195, '[\"ROLE_CLIENT\"]', '$2y$13$Ca/6Acdn3V0M2iHnF4VD1OEUg8N1YdTlVyRki5LRaXljk81QScvu2', 'ESCAYG', 'MARIE JOSEE', NULL, '<EMAIL>', '0696766765', NULL, 1, '2022-11-09 00:34:46', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'ESCAYG', NULL, NULL, NULL),
(196, '[\"ROLE_CLIENT\"]', '$2y$13$q.XPMmGG2NUayho.b1dQqOT/k6y4FT52PyqhOFkivmQrMJ.ES3RbO', 'BOUCHER', 'MARIE JOSEE', NULL, '<EMAIL>', '5146545480', NULL, 1, '2022-11-19 02:15:32', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'BOUCHER', NULL, NULL, NULL),
(197, '[\"ROLE_CLIENT\"]', '$2y$13$dNrsv9QuIGbD79byD5ZHpeTNg2xoRQE1n9AP8lV9L7aeFuPC/P3fe', 'DR TERRIER', 'mohammed', NULL, '<EMAIL>', '0667234192', NULL, 1, '2022-11-20 16:24:51', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'DR TERRIER', NULL, NULL, NULL),
(198, '[\"ROLE_CLIENT\"]', '$2y$13$QpXSJwMasyLOmkiu2MnekOAt8Xqbmy3QZ5.jeT10AQPF9hpUUO/c6', 'Martin', 'Alain', NULL, '<EMAIL>', '+33297362846', NULL, 1, '2022-11-24 21:13:32', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Martin', NULL, NULL, NULL),
(199, '[\"ROLE_CLIENT\"]', '$2y$13$7xCnHxoaf9KxUK0AwWSQyeBe0OVNW3l4RNSD6ZIDIxDfRFJtDlday', 'MOUTON', 'FRANCOISE', NULL, '<EMAIL>', '0032475208999', NULL, 1, '2022-11-28 14:55:02', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'MOUTON', NULL, NULL, NULL),
(200, '[\"ROLE_CLIENT\"]', '$2y$13$BcWMO4l47E4tOOnFty337O6IS7MOkBW6jbU1P2e1Cakj0rVoMHlqy', 'Roca', 'Erick', NULL, '<EMAIL>', '0669583475', NULL, 1, '2022-12-02 04:19:20', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Roca', NULL, NULL, NULL),
(201, '[\"ROLE_CLIENT\"]', '$2y$13$4s0vmzJJ8FjhUi.f6wD6Eu/JGvAtHjTohI8AT5.XMnVdb6QdnXpiq', 'Rimée', 'Philippe', NULL, '<EMAIL>', '+32475808999', NULL, 1, '2022-12-06 03:27:31', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Rimée', NULL, NULL, NULL),
(202, '[\"ROLE_CLIENT\"]', '$2y$13$asr5SdDvxHhBWSBWgoYHqua5f4A6fFguXAHfjdcd0.dY0Sezl0XnC', 'Gournet', 'Gournet Sophie', NULL, '<EMAIL>', '+33675810858', NULL, 1, '2022-12-09 14:47:33', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Gournet', NULL, NULL, NULL),
(203, '[\"ROLE_CLIENT\"]', '$2y$13$4dDgpplGfqEjRpwhZt6z7OEYAb61G8RVHj6NFDWhxwYQcwMCll.kG', 'MALICI', 'MALICI', NULL, '<EMAIL>', '252525252', NULL, 1, '2022-12-09 23:49:11', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'MALICI', NULL, NULL, NULL),
(204, '[\"ROLE_CLIENT\"]', '$2y$13$getI95Q9HIs.vZ4R.G00ceTulydnvyWai2ZTwg/obOj3e8KlvR/ga', 'BEAUNOL', 'JULIEN', NULL, '<EMAIL>', '+687508899', NULL, 1, '2022-12-11 16:32:10', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'BEAUNOL', NULL, NULL, NULL),
(205, '[\"ROLE_CLIENT\"]', '$2y$13$nqg7K.Rkay.MP2hVuyl9auoWztMa2LahtOAOUZ0UUsQCIMaHtDJ.W', 'phobere', 'sandra-gislaine', NULL, '<EMAIL>', '+33665350228', NULL, 1, '2022-12-14 22:45:34', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'phobere', NULL, NULL, NULL),
(206, '[\"ROLE_CLIENT\"]', '$2y$13$ZUaeJT.F22HxeWVNE3Kn8.Z6qNtqaZdxouduACOqu2JPewJ.qm5fe', 'BARRIERE', 'ROSALIE', NULL, '<EMAIL>', '0749537155', NULL, 1, '2022-12-27 00:43:16', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'BARRIERE', NULL, NULL, NULL),
(207, '[\"ROLE_CLIENT\"]', '$2y$13$TnrdWl1AOp3KPuNTbMbrgOYpiJwZ19KikyJF00sXdIr1IzN3DVMO2', 'Baker', 'leatitia', NULL, '<EMAIL>', '0690759534', NULL, 1, '2023-01-04 04:53:28', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Baker', NULL, NULL, NULL),
(208, '[\"ROLE_CLIENT\"]', '$2y$13$G.zedk.eS78jyoNAQYpxcuqeWb3q1iEHrE2gVEL8pa9z3R8fZ4K3y', 'LABRADOR', 'ASTRID', NULL, '<EMAIL>', '+33627831693', NULL, 1, '2023-01-04 21:37:38', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'LABRADOR', NULL, NULL, NULL),
(209, '[\"ROLE_CLIENT\"]', '$2y$13$JnEWtqCkeduqxjWaoiBUqevRgeAQdOF.2nmdSqudbKqTU9sHdLcE.', 'Raphaelle Quint', 'Raphaelle Quint', NULL, '<EMAIL>', '+ 33 6 69 25 64 22', NULL, 1, '2023-01-10 02:17:43', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Raphaelle Quint', NULL, NULL, NULL),
(210, '[\"ROLE_CLIENT\"]', '$2y$13$MkhjUEL9P8dFztGFwYnvGuTAf49Q.nOyuE1dci/v/3pzpmVECZGhu', 'nullens', 'gilles', NULL, '<EMAIL>', '00000000', NULL, 1, '2023-01-12 20:51:08', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'nullens', NULL, NULL, NULL),
(211, '[\"ROLE_CLIENT\"]', '$2y$13$VtjTGc1Ua9GTJnNRlWf2WOsdWaISTfaSSdIRCwur2cBJA2RjfBxaG', 'alloin', 'bernard', NULL, '<EMAIL>', '00000000', NULL, 1, '2023-01-13 23:15:46', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'alloin', NULL, NULL, NULL),
(212, '[\"ROLE_CLIENT\"]', '$2y$13$XbHwZT.JV3nozyIhakT95uABCyr4MJMVVvfvk115QjJKFiU74lJFe', 'BRIAND', 'Sabrina', NULL, '<EMAIL>', '000000', NULL, 1, '2023-01-14 23:49:18', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'BRIAND', NULL, NULL, NULL),
(213, '[\"ROLE_CLIENT\"]', '$2y$13$BRSrnrW49kOOza7F5e/DQurKJSsFo8LMy/80LlUCqzjlIUrZVFDfy', 'vincent', 'romain', NULL, '<EMAIL>', '0690934814', NULL, 1, '2023-01-17 21:24:15', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'vincent', NULL, NULL, NULL),
(214, '[\"ROLE_CLIENT\"]', '$2y$13$xwYP4SFTUBc5Z2em1YKA3u/UXRxCF.LiBL9DoZX8i0bzFbh3Wc6Se', 'LOUISE', 'Alicia', NULL, '<EMAIL>', '00000', NULL, 1, '2023-01-22 16:50:10', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'LOUISE', NULL, NULL, NULL),
(215, '[\"ROLE_CLIENT\"]', '$2y$13$Sq7yibnTAnXlgJzAXnVpgeMTWOqtS7vihuaERruXTLD5a8C366Dyu', 'Vigue', 'Paul', NULL, '<EMAIL>', '0000', NULL, 1, '2023-01-24 23:05:18', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Vigue', NULL, NULL, NULL),
(216, '[\"ROLE_CLIENT\"]', '$2y$13$9E2PFjdOLt2FSlyGqpRLfeZslkLok1yWS9UnkleZdu1cpQnu8VKGW', 'Putois', 'Maxence', NULL, '<EMAIL>', '000000', NULL, 1, '2023-01-25 15:11:53', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Putois', NULL, NULL, NULL),
(217, '[\"ROLE_CLIENT\"]', '$2y$13$k.2GaSoD.x.RKRpMXnsCBew10E4SW4Lts6t2eV7wL7QAFWv0cc6Gy', 'delobelle', 'jean-marc', NULL, '<EMAIL>', '0663120458', NULL, 1, '2023-03-14 16:12:37', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'delobelle', NULL, NULL, NULL),
(218, '[\"ROLE_CLIENT\"]', '$2y$13$KGYFPEf49zZqD0juRHKhPO9h2JLA4E5fP0vJ9.4Z/GnM4rhQcyjyy', 'linel', 'marc alexandre', NULL, '<EMAIL>', '0672166953', NULL, 1, '2023-04-20 06:18:26', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'linel', NULL, NULL, NULL),
(219, '[\"ROLE_CLIENT\"]', '$2y$13$LOuhY8kircmP7NkypBdFvOlX3c6zCQinWCJomUVf9qKlmcWKjwGZa', 'redon', 'josiane', NULL, '<EMAIL>', '0781397051', NULL, 1, '2023-05-04 00:12:30', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'redon', NULL, NULL, NULL),
(220, '[\"ROLE_CLIENT\"]', '$2y$13$XMewgWnHjleExC0Zc6zjne7B9OI.RyvEjek/WNi50qTm09uH7.1R6', 'HOTON', 'GEORGY JOSUE', NULL, '<EMAIL>', '+33751895183', NULL, 1, '2023-05-05 21:33:16', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'HOTON', NULL, NULL, NULL),
(221, '[\"ROLE_CLIENT\"]', '$2y$13$79oSUB2ZJGoryM9hL4HXF.CINjxLJ0NbvKpzpwY8eFN0SjzOmnZYy', 'GARGAR', 'Marc', NULL, '<EMAIL>', '0666763478', NULL, 1, '2023-05-27 01:42:25', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'GARGAR', NULL, NULL, NULL),
(222, '[\"ROLE_CLIENT\"]', '$2y$13$5tOGRdvxEBc5tepELxACbuTcC0tRnCQ.DMdL24KGMrriSt7XmxZFW', 'SOLVET', 'laure', NULL, '<EMAIL>', '+33619211874', NULL, 1, '2023-05-27 04:23:30', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'SOLVET', NULL, NULL, NULL),
(223, '[\"ROLE_CLIENT\"]', '$2y$13$qPjlaMvCkVl0aFAOaKaAh.milsDXN1Wza9H0Zw3.I4gq6wI/TGyoe', 'Ricard', 'Olivier', NULL, '<EMAIL>', '0782117585', NULL, 1, '2023-05-29 00:51:59', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Ricard', NULL, NULL, NULL),
(224, '[\"ROLE_CLIENT\"]', '$2y$13$EWk.qQlr3OD4A79/jMRLqO2qusMiOm9RzGMpgThASpCzK4aTGc1zC', 'Jeannello', 'Célia', NULL, '<EMAIL>', '595', NULL, 1, '2023-06-02 20:38:25', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Jeannello', NULL, NULL, NULL),
(225, '[\"ROLE_CLIENT\"]', '$2y$13$WXaPI.j1HfcJ.uq65m4oWetODYpcvUGyZyTfz1/48Jq.zUc.94wFm', 'LABRADOR', 'gontran', 'SECTION CARRERE LES GRANDS FONDS', '<EMAIL>', '0690454147', '', 1, '2023-06-08 06:02:56', NULL, NULL, '1962-03-28', '6200014', '2006-05-15', 'LE MOULE', NULL, 'LE MOULE', 97160, 'POINTE A PITRE', 'LABRADOR', 19, 19, 'Mr'),
(226, '[\"ROLE_CLIENT\"]', '$2y$13$hkvh1OhtuH9qNjjsAyGnPebJxIQhmp7lD0OqyzqxZzajKqraqSEbC', 'GANDON', 'francois andre', '29 AV', '<EMAIL>', '+33684910598', '+33684910598', 1, '2023-06-10 17:36:56', NULL, NULL, NULL, NULL, NULL, NULL, 'COMMUE DE PARIS', 'VITRY SUR SEINE', 94400, NULL, 'GRANDON', 11, 11, NULL),
(227, '[\"ROLE_CLIENT\"]', '$2y$13$uZ0gEieBTSQFIsBzQphj4.XFZY9FqmrJWQay3U9Szu2tObz4ceXRa', 'LATAPIE', 'Sylvaine', NULL, '<EMAIL>', '+33663678992', NULL, 1, '2023-06-16 14:52:01', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'LATAPIE', NULL, NULL, NULL),
(228, '[\"ROLE_CLIENT\"]', '$2y$13$cE2F21drwX5OhGFzWW9V7ef6J2Rl8xETMewYPgWVB5P/gonDmxu7C', 'renoir', 'audrey', NULL, 'audrey.renoir@outlook.f', '0750989981', NULL, 1, '2023-06-20 13:36:09', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'renoir', NULL, NULL, NULL),
(229, '[\"ROLE_CLIENT\"]', '$2y$13$mXb7h.QP9W4XBo3SCWIdfOn/qUL1nUCKhCmj4BoMQ24csW1pZ7Xpe', 'deveaux', 'eddy', NULL, '<EMAIL>', '0781265449', NULL, 1, '2023-07-03 15:35:05', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'deveaux', NULL, NULL, NULL),
(230, '[\"ROLE_CLIENT\"]', '$2y$13$NMo6y8wgeWnK.RB4nG9i9usbV8vy9YaaNfwfXOYo1F3Gp.MLeyOq6', 'tancelin', 'OLIVIER', NULL, '<EMAIL>', '+33767314604', NULL, 1, '2023-08-01 15:04:44', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'tancelin', NULL, NULL, NULL),
(231, '[\"ROLE_CLIENT\"]', '$2y$13$JzkWETt7OEVTq0pxnsjPhO/mDcZY99ux0xTA9JzJm9ErOzS1ES/aS', 'eric', 'ouanna', NULL, '<EMAIL>', '1111', NULL, 1, '2023-08-01 15:49:30', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'eric', NULL, NULL, NULL),
(232, '[\"ROLE_CLIENT\"]', '$2y$13$Q35RagoWuNtz4oCLhCssGeUzSiEunIis/3mJhS3u3h7xCpfXcAkke', 'romain', 'jean-pierre', NULL, '<EMAIL>', '+33984112781', NULL, 1, '2023-08-09 04:41:46', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'romain', NULL, NULL, NULL),
(233, '[\"ROLE_CLIENT\"]', '$2y$13$CSEUtxlPe5lOUlyOrQ1LZeiPkqJbmoIR/WYSbtn3GwiAfVXOLuaOS', 'Sylvaine', 'Marie', NULL, '<EMAIL>', '+33681417928', NULL, 1, '2023-08-11 01:24:16', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Sylvaine', NULL, NULL, NULL),
(234, '[\"ROLE_CLIENT\"]', '$2y$13$EXFuFalud4au8OOMtNTineE9AcnrZhFosoT7V1kK/sZVz24wonsha', 'pepin', 'marie', NULL, '<EMAIL>', '07 86 77 86 47', NULL, 1, '2023-08-12 03:02:44', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'pepin', NULL, NULL, NULL),
(235, '[\"ROLE_CLIENT\"]', '$2y$13$22uo9Eu25i79lvkulRq6/u/arHLRpqbaX262AgpuI5sWVodtsuj1y', 'pepin', 'pierre carole', NULL, '<EMAIL>', '07 86 77 86 47', NULL, 1, '2023-08-12 03:06:08', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'pepin', NULL, NULL, NULL),
(236, '[\"ROLE_CLIENT\"]', '$2y$13$G99a20j/AS9n1LGCP2zNROMoM7RUam2Gmj0uWMN03Ln78GpfNz5kW', 'phobere', 'steve michel', NULL, '<EMAIL>', '+590690999731', NULL, 1, '2023-08-21 16:19:11', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'phobere', NULL, NULL, NULL),
(237, '[\"ROLE_CLIENT\"]', '$2y$13$bpQceYDArp2r2KqSM/Rs9ewGwgVzPd1hUG6JabxWVaqJ6KJB24q5i', 'schummer', 'Philippe', NULL, '<EMAIL>', '0033623178185', NULL, 1, '2023-08-21 18:28:15', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'schummer', NULL, NULL, NULL),
(238, '[\"ROLE_CLIENT\"]', '$2y$13$GspEEhrQhREZCLKFdWLlSOX4sfJnH4MeLMJoVYPT44DNiHs1l43xu', 'dupupet-launay', 'nadine', NULL, '<EMAIL>', '0618277695', NULL, 1, '2023-09-06 02:01:37', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'dupupet-launay', NULL, NULL, NULL),
(239, '[\"ROLE_CLIENT\"]', '$2y$13$QGgN1yqk4Jq2DL9BYcLNCOAxnhFPlZerB3TzTDWaBdp1AKeZYDcH2', 'thirant', 'pascale', NULL, '<EMAIL>', '000500000', NULL, 1, '2023-09-18 16:21:53', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'thirant', NULL, NULL, NULL),
(240, '[\"ROLE_CLIENT\"]', '$2y$13$LChb0ciKuPKnsdeBOBXh0eDYcLM3ugoS/ABiYS0N3w74fHBYOLJxC', 'FABIGNON', 'Suzette', NULL, '<EMAIL>', '06 82 83 99 14', NULL, 1, '2023-10-05 20:53:16', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'FABIGNON', NULL, NULL, NULL),
(241, '[\"ROLE_CLIENT\"]', '$2y$13$CSrNegXmmBPJwCSuateG0.0FjnD5v1v6W2Gh2loHoU9hMt/EMWtRi', 'lama', 'willene.catherine', NULL, '<EMAIL>', '0690590710', NULL, 1, '2023-10-12 07:08:49', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'lama', NULL, NULL, NULL),
(242, '[\"ROLE_CLIENT\"]', '$2y$13$CmeLlEe3crVIkoIvkBzzHOp3Fa3yFtXirVQGw/aC9DTUU3xbs2oMS', 'Mme Halvin', 'halvin', NULL, 'dorine.halvin@hotmail.f', '00000000000000005', NULL, 1, '2023-10-24 21:01:32', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Mme Halvin', NULL, NULL, NULL),
(243, '[\"ROLE_CLIENT\"]', '$2y$13$i0jpRHvDImWEqsB4Bze5.eLrlKiNVYTlnIKfZWV1Arkn5YZyKWal2', 'Beaunol', 'Murielle', NULL, '<EMAIL>', '+687508899', NULL, 1, '2023-10-30 15:42:33', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Beaunol', NULL, NULL, NULL),
(244, '[\"ROLE_CLIENT\"]', '$2y$13$eDrpZaP/UzRLdLv13Vjb1eIyLyaR10zb6etYjfzUxKfjLzaeKMTti', 'Bouget', 'Manon', NULL, '<EMAIL>', '+33689049271', NULL, 1, '2023-11-07 02:44:40', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Bouget', NULL, NULL, NULL),
(245, '[\"ROLE_CLIENT\"]', '$2y$13$46Bn09Ag/ej9bTWmjrdsPO5LOSnQVeFZq8MwwXTbDtiDg5yV2U/JG', 'CAUSSANEL', 'ARNAUD', NULL, '<EMAIL>', '06.32.46.18.71', NULL, 1, '2023-12-04 17:28:24', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'CAUSSANEL', NULL, NULL, NULL),
(248, '[\"ROLE_CLIENT\"]', '$2y$13$9xXD9xrnaIO61oKFELUTrOOowux09zZGMt34ZiRDqPvtV4RShred2', 'MARTY', 'CLAUDE GERALD', 'LIEU DIT LA RIVOIRE', '<EMAIL>', '+33625521352', '+33625521352', 1, '2023-12-12 19:11:05', NULL, NULL, NULL, NULL, NULL, NULL, '7 LOTISSEMENT LA ROSERAIE', 'SEYSSUEL', 38200, NULL, 'MARTY', 15, 15, NULL),
(249, '[\"ROLE_CLIENT\"]', '$2y$13$wkGAu3DehBXHZJKRsfPvtOwz8yR5QmtwrY47BVAhG1XiIGkPzzMVC', 'halvin', 'claudine', NULL, '<EMAIL>', '0769917211', NULL, 1, '2023-12-13 19:25:12', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'halvin', NULL, NULL, NULL),
(250, '[\"ROLE_CLIENT\"]', '$2y$13$sMJJA1ROG4RbMDFdHtbRkOmrrcprkApMGGvaAo9cs0/qo3Zj8RQOS', 'CORVO', 'Audrey', NULL, '<EMAIL>', '00', NULL, 1, '2023-12-16 04:22:56', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'CORVO', NULL, NULL, NULL),
(251, '[\"ROLE_CLIENT\"]', '$2y$13$.SwUEZpQ0lNpGn9gWnRrJOaE3Wi2gbB4mXD74N82bGvAq7Qqrpqs.', 'plumecocq', 'michaela', 'mm', '<EMAIL>', '+33646611816', '66', 1, '2023-12-24 15:52:02', NULL, NULL, NULL, NULL, NULL, NULL, '9jhuj', 'gtuio', 6562, NULL, 'ELOIKA', 13, 13, NULL),
(252, '[\"ROLE_CLIENT\"]', '$2y$13$qeZAVyj2eT6C9I1jKNnPTeKKUwoY3ATfB2zpuX5tL2cUVwxuvUQ7.', 'Miermont Laure', 'Miermont Laure', NULL, '<EMAIL>', '0678472029', NULL, 1, '2023-12-25 17:12:34', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Miermont Laure', NULL, NULL, NULL),
(253, '[\"ROLE_CLIENT\"]', '$2y$13$tmpxhDaphaM8NYg96c9T7.SzHFzP4SdS920ars29FBDyMvWiwv9Dm', 'ANDREMONT', 'JOSIANE CELINE', NULL, '<EMAIL>', '0690308534', NULL, 1, '2024-01-05 03:53:58', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'ANDREMONT', NULL, NULL, NULL),
(254, '[\"ROLE_CLIENT\"]', '$2y$13$PRtnSFUkgsVOQOcaY56hb.HuvJ4jZva32cWW1Roz1hrZZC6em7noW', 'capitolin', 'lucien/lambert', NULL, '<EMAIL>', '0690366507', NULL, 1, '2024-01-07 08:33:28', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'capitolin', NULL, NULL, NULL),
(255, '[\"ROLE_CLIENT\"]', '$2y$13$EYJQPY76stpCgdgAQxk5a.U4.3jSYyR2ykABS5rpPhMjd6965FQGi', 'Jeancelme', 'Mathilde', NULL, '<EMAIL>', '06 69 73 22 36', NULL, 1, '2024-01-09 02:47:36', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Jeancelme', NULL, NULL, NULL),
(256, '[\"ROLE_CLIENT\"]', '$2y$13$XpyCOsrpk2VZwWDyjRnbD.BqHVG.HrQCccZlRRak.81oZF71ggEzu', 'pujo', 'andre', '16 camin dera castera', '<EMAIL>', '+33685422983', '+33685422983', 1, '2024-01-10 21:52:27', NULL, NULL, NULL, NULL, NULL, NULL, '.', 'arcizans avant', 65400, NULL, 'bourdon', 14, 14, NULL),
(257, '[\"ROLE_CLIENT\"]', '$2y$13$7jG1oVf6n7bqKIO1BxQzR.iFDwnNaVpI3VB8iLyeD5samqj5XazXe', 'SOLVET', 'AYMAR', NULL, '<EMAIL>', '0620496505', NULL, 1, '2024-01-26 03:36:18', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'SOLVET', NULL, NULL, NULL),
(258, '[\"ROLE_CLIENT\"]', '$2y$13$3AI4UxnFhEVdgtyCLczpdu4iUkoVKNsWxkpsuKSPiszjmnQ7uZdFC', 'PIERRE-LOUIS', 'Wendy', NULL, '<EMAIL>', '+33646159519', NULL, 1, '2024-01-28 04:42:52', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'PIERRE-LOUIS', NULL, NULL, NULL),
(259, '[\"ROLE_CLIENT\"]', '$2y$13$wgVA3XLJCLnSFY/u/GK1S.USUoEed6QmEUuORKlrzz7yyQDJ4qvnu', 'BALTYDE', 'Jacky', NULL, '<EMAIL>', '0684995314', NULL, 1, '2024-02-07 04:58:44', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'BALTYDE', NULL, NULL, NULL),
(260, '[\"ROLE_CLIENT\"]', '$2y$13$9EE9ci53oEFAJFuk.lKaKevxQ9HaGdzE562fb9zwv0gB9wdrqVrD2', 'Arditi', 'Arditi', NULL, '<EMAIL>', '0678607248', NULL, 1, '2024-02-09 04:31:14', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Arditi', NULL, NULL, NULL),
(261, '[\"ROLE_CLIENT\"]', '$2y$13$KvGrGhYSHi7tr2fFt3AsSODDO7oWPuTlEzytZoYYxofJzlz6BQco2', 'MARIE URSULE', 'MYRIAM', NULL, '<EMAIL>', '0690625640', NULL, 1, '2024-02-11 00:45:22', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'MARIE URSULE', NULL, NULL, NULL),
(262, '[\"ROLE_CLIENT\"]', '$2y$13$WALN5inoErd0TVbGNVaS3.66vm7YLCEJDKjscbfBTpoBFP.4onNPW', 'FARGEON', 'Aurélie', NULL, '<EMAIL>', '0769230428', NULL, 1, '2024-02-16 21:00:33', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'FARGEON', NULL, NULL, NULL),
(263, '[\"ROLE_CLIENT\"]', '$2y$13$6SDNR9zoyB0i0OXcOO8Nuu/lZXvoxRy.KHa0bWkzdZEl6Kr4unDam', 'COLIN', 'CHISTOPHE', NULL, '<EMAIL>', '0690803767', NULL, 1, '2024-02-20 01:53:03', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'COLIN', NULL, NULL, NULL),
(264, '[\"ROLE_CLIENT\"]', '$2y$13$cLD4NqqyL.z5LEMl1O0KjO7liAMCNDH7TwXqPeBYsJyCLEEjQGZ5y', 'MONESTIER', 'FREDDY GUY', NULL, '<EMAIL>', '0690457077', NULL, 1, '2024-02-23 02:02:34', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'MONESTIER', NULL, NULL, NULL),
(265, '[\"ROLE_CLIENT\"]', '$2y$13$bH5GapBoiSwF21JGP8fq9.Fm4psecWtmCSjrvDSEaRNu4iFGEpTw.', 'PESQUET', 'Mathilde', NULL, '<EMAIL>', '6565+666+', NULL, 1, '2024-02-27 21:31:16', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'PESQUET', NULL, NULL, NULL),
(266, '[\"ROLE_CLIENT\"]', '$2y$13$ziqMzJuG4P9j4nXAEFZYUeWOpkbmPtW6u.czy66xUy0wI0sIUS1b2', 'M.  Gauville', 'Jonathan', NULL, '<EMAIL>', '0678368639', NULL, 1, '2024-02-29 21:07:16', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'M.  Gauville', NULL, NULL, NULL),
(267, '[\"ROLE_CLIENT\"]', '$2y$13$mx3oBhxZsY.TEv/rhPTPleBSq5SQEFKl9nAIudretE5S0ufgBKsO.', 'M. BLANCHARD', 'JEAN CLAUDE', NULL, '<EMAIL>', '0648815576', NULL, 1, '2024-03-01 19:44:35', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'M. BLANCHARD', NULL, NULL, NULL),
(268, '[\"ROLE_CLIENT\"]', '$2y$13$JofNZj8Nvk28GGfXnl0za.m2vD.wJo8CupDSd3X/dT492McMI8xNa', 'SIRISACK', 'PHOMMACHANH', NULL, '<EMAIL>', '+33651610376', NULL, 1, '2024-03-01 22:18:49', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'SIRISACK', NULL, NULL, NULL),
(269, '[\"ROLE_CLIENT\"]', '$2y$13$.RTw3BYqliVF9zBN1hqAxOGfWsRNGbtDBqUGC.dcPTeQgdwxbQ9He', 'PETIT', 'JAYSON JOEL', NULL, '<EMAIL>', '+337757797945', NULL, 1, '2024-03-13 22:17:16', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'PETIT', NULL, NULL, NULL),
(270, '[\"ROLE_CLIENT\"]', '$2y$13$1C8OeOVEy0oB7AJP8wGLy.oIqStb.huchjCaau4cHoMYb4Q8F7CH2', 'NABAL', 'Lydia', 'saint escobille', '<EMAIL>', '0761778504', '0761778504', 1, '2024-03-14 17:19:15', NULL, NULL, NULL, NULL, NULL, NULL, '.', '.', 97160, NULL, 'NABAL', 16, 16, NULL),
(271, '[\"ROLE_CLIENT\"]', '$2y$13$GGBb0kN4NzlICUQoI0TFCerkSqE9yFku8.qMGHZiCddkwXAHX6QaS', 'gallonde', 'alizee jeanne', NULL, '<EMAIL>', '+596696249579', '0000000', 1, '2024-03-16 22:28:55', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'gallonde', 17, 17, NULL),
(272, '[\"ROLE_CLIENT\"]', '$2y$13$X./Dk93CAtLHV.Nsf68D6ugRg/JtguzthSrmUQ12AfQD5Tmno3SIG', 'BONBAL', 'DIDIER GILLES PATRICK', NULL, '<EMAIL>', '+33678368639', NULL, 1, '2024-03-22 20:26:16', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'BONBAL', NULL, NULL, NULL),
(273, '[\"ROLE_CLIENT\"]', '$2y$13$7yWuNxF63He43fycH23XU.x6CMSgMzgkIt20wpE8SO/a5./r/hSU6', 'DUPRAT', 'FRANCK', NULL, '<EMAIL>', '+33677019076', NULL, 1, '2024-03-30 20:24:17', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'DUPRAT', NULL, NULL, NULL),
(274, '[\"ROLE_CLIENT\"]', '$2y$13$x95Zdif5OAndHYp69PHSuO0moFymjvkIvSu1DyEcK16LMIvRdGfqa', 'Pougeol', 'Teddy', NULL, '<EMAIL>', '+33672230586', NULL, 1, '2024-03-30 21:07:55', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Pougeol', NULL, NULL, NULL),
(275, '[\"ROLE_CLIENT\"]', '$2y$13$pfDEJyGi7Cr0vYc.1d5loOac1kX9nrhcK5bjyza.2kWHw4rlLMPvy', 'BARUL', 'OLIVIER JEAN-LUC', NULL, '<EMAIL>', '+33658296065', NULL, 1, '2024-04-02 03:53:55', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'BARUL', NULL, NULL, NULL),
(276, '[\"ROLE_CLIENT\"]', '$2y$13$J2P8bGeddRXbxCD24ePBaO88Rp.oHl54cs4QNT9h0VtkYnrvfI00e', 'BARBIER', 'DOMINIQUE MARIANE', NULL, '<EMAIL>', '+590690627620', NULL, 1, '2024-04-02 16:30:40', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'BARBIER', NULL, NULL, NULL),
(277, '[\"ROLE_CLIENT\"]', '$2y$13$Qej67eoyNuNVx.4sA4/lmORLJUgIiaDM1sTJ.TMVlxc7Mc6g6YlWm', 'Chandler', 'Michel', NULL, '<EMAIL>', '0677631505', NULL, 1, '2024-04-05 04:31:54', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Chandler', NULL, NULL, NULL),
(278, '[\"ROLE_CLIENT\"]', '$2y$13$lzPfDM8e2HgwRnC4G6KEx.4tL8pMFPrOumrNrni3qfZqlVa3tKm6C', 'ruello', 'marc maurice robert', NULL, '<EMAIL>', '0000000000000000', NULL, 1, '2024-04-06 21:47:56', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'ruello', NULL, NULL, NULL),
(279, '[\"ROLE_CLIENT\"]', '$2y$13$SUnzKijvtXTDLsbwJrysKeQWd8YxMeqJvpHYgK5bVgp7Qq6Hr1Zd6', 'ducelier', 'FRANCK', NULL, '<EMAIL>', '0629496663', NULL, 1, '2024-04-12 17:58:17', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'ducelier', NULL, NULL, NULL),
(280, '[\"ROLE_CLIENT\"]', '$2y$13$FMUO9yJnRBef4pSyEQGhteNxyTEeR9S8wnxml.lOkh0sWq8ck0/he', 'guyard', 'beryl', NULL, '<EMAIL>', '+33677047474', NULL, 1, '2024-04-16 18:45:27', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'guyard', NULL, NULL, NULL),
(281, '[\"ROLE_CLIENT\"]', '$2y$13$kkqKkpYPCQwfQ4AroMop0.B0Nj5vKeomykWhRXKajc2M.d1LKJkle', 'MILORD', 'CLIFORD', NULL, '<EMAIL>', '+590 690 92-9883', NULL, 1, '2024-04-23 00:44:31', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'MILORD', NULL, NULL, NULL),
(282, '[\"ROLE_CLIENT\"]', '$2y$13$xzB0eSg39cskZQR6gu6mleUSO8R4xhMbpsCP0JTgNp2/P2EIpAn6m', 'MITEL', 'KAREN MIRELLA', NULL, '<EMAIL>', '0690214071', NULL, 1, '2024-05-02 04:46:31', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'MITEL', NULL, NULL, NULL),
(283, '[\"ROLE_CLIENT\"]', '$2y$13$wMpIax8g3XBWyeFh3nQJbeE2q0k0W7ikjF/ZfcLwpU5EF1DD1WApe', 'FLESSEL', 'JEAN NICOLAS', NULL, '<EMAIL>', '+33767355334', NULL, 1, '2024-05-08 04:53:01', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'FLESSEL', NULL, NULL, NULL),
(284, '[\"ROLE_CLIENT\"]', '$2y$13$CqaALAAGmM7OlBRKVR4svecGjksV2LKBdIIpZQ75B5.KZv/rrGgN.', 'JOSE', 'ROY', NULL, '<EMAIL>', '56+565+96', NULL, 1, '2024-06-28 05:14:47', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'JOSE', NULL, NULL, NULL),
(285, '[\"ROLE_CLIENT\"]', '$2y$13$IpaDJ9K67CseyDc3QEH7ueLkNFJjtV3FSCIRBk2LCjLCgal7AN2Iu', 'accajou', 'swann', NULL, '<EMAIL>', '+33662649459', NULL, 1, '2024-07-05 00:08:51', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'accajou', NULL, NULL, NULL),
(286, '[\"ROLE_CLIENT\"]', '$2y$13$K2/w7juxC45fbQgRA9/SEuoR1KziinA.oNr/mhB1k8vKVtfVM75nO', 'Johnson', 'Johnson', NULL, '<EMAIL>', '***********.91', NULL, 1, '2024-07-10 17:44:29', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Johnson', NULL, NULL, NULL),
(287, '[\"ROLE_CLIENT\"]', '$2y$13$tv/.N6rEROt0pcajSPak0.EODiNn9vb/GpBjS8u97oDvgp2amFFhS', 'BOLUS', 'Marc', NULL, '<EMAIL>', '+447950914545', NULL, 1, '2024-07-11 16:12:42', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'BOLUS', NULL, NULL, NULL),
(288, '[\"ROLE_CLIENT\"]', '$2y$13$pDLT6YP.PB5XRQ2dcsrjI.6HxJO6CTGi/DU6fC5lc.AvHWSrllIla', 'COLOGER', 'MICHELINE SYLVIE', NULL, '<EMAIL>', '0749188589', NULL, 1, '2024-07-19 13:34:14', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'COLOGER', NULL, NULL, NULL),
(289, '[\"ROLE_CLIENT\"]', '$2y$13$7GBs6Osp0qOhZFjBBdvd.u1qGxjH0phVK9/n7Agi9ukA1XzZFtfpy', 'M. SAUNIERE', 'JEROME', NULL, '<EMAIL>', '+33681417955', NULL, 1, '2024-08-13 04:16:31', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'M. SAUNIERE', NULL, NULL, NULL),
(290, '[\"ROLE_CLIENT\"]', '$2y$13$Mjw4gvfIbTYaHRU6pEROoersJh/./FlHfcq7F6deXqEU.B6YCRiI.', 'GUYOT', 'DANIEL', NULL, '<EMAIL>', '+33615208427', NULL, 1, '2024-08-17 22:42:25', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'GUYOT', NULL, NULL, NULL),
(291, '[\"ROLE_CLIENT\"]', '$2y$13$VXeSIIYhlJc7UZEy48InaOi/pu1nmsPayj01Otu7Tr406vwlS.CIy', 'NEPAUL', 'MARCELIN', NULL, '<EMAIL>', '+33782417768', NULL, 1, '2024-08-20 18:35:22', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'NEPAUL', NULL, NULL, NULL),
(292, '[\"ROLE_CLIENT\"]', '$2y$13$uQ.9jimbjaHAarqNI79zf.QVzx13wmYdUN3C5oXrO09/MgTId1rTa', 'HELDER', 'MARQUES', NULL, '<EMAIL>', '+33645832360', NULL, 1, '2024-09-07 06:13:44', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'HELDER', NULL, NULL, NULL),
(293, '[\"ROLE_CLIENT\"]', '$2y$13$9CfdRdqBuHxepYbqmguVDeix54wzgoENJFV7nHfIdIBj2b6ae06l.', 'PIRONAL', 'SYLIANNE CLARISSE', NULL, '<EMAIL>', '+590690801019', NULL, 1, '2024-09-24 00:59:57', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'PIRONAL', NULL, NULL, NULL),
(294, '[\"ROLE_CLIENT\"]', '$2y$13$Qzy3GeGwwB4FG3nYhMtR8Ohn.LRU7rMB2ZP7hFlWNS5EVsZG./HeS', 'BARIGHTON', 'AUDREY', NULL, '<EMAIL>', '06.18.27.73.15', '', 1, '2024-09-25 03:19:19', NULL, NULL, '2024-09-20', 'KNL.MLM665', '2024-09-19', 'POINT A PITRE', NULL, NULL, NULL, 'PAP', 'BARIGHTON', 18, 18, 'Mme'),
(295, '[\"ROLE_CLIENT\"]', '$2y$13$wEDLlFADoAcN/pjUY1vV4.E7nuIMvi0uAfoblg6n/MvKSCiAsZ2He', 'Launay', 'FREDERIC', '16 LIEU DIT JEGU', '<EMAIL>', '0760268688', '', 1, '2024-09-25 18:54:13', NULL, NULL, '1971-05-05', '90102241095', '1991-04-12', 'PABU', NULL, 'QUESSOY', 22120, 'PREFET DES COTE D ARMOR', 'Launay', 22, 22, 'Mr'),
(296, '[\"ROLE_CLIENT\"]', '$2y$13$uWp90K5ANFmX4kG1bdLL4uFTV.0wWW8ArF5dd69eyaj7mKTi5zjxO', 'DACOURT EPOUSE CHAUPARD', 'SABRINA NADEGE', 'route de cluny chateau gaillard', '<EMAIL>', '0690239214', '', 1, '2024-09-26 23:29:56', NULL, NULL, '1978-08-02', '970796100302', '2010-06-11', 'POINT A PITRE', NULL, 'le moule', 97160, 'pointre a pitre', 'DACOURT EPOUSE CHAUPARD', 20, 20, 'Mme'),
(297, '[\"ROLE_CLIENT\"]', '$2y$13$MYjA4pCnWmsGjq2kja7K1eLN9bE9dZuwV5x6GPMRCcJh218YD9mKO', 'MANGO', 'MARVIN BERNARD', '102 BIS JISORS MONTEAUBAN', '<EMAIL>', '0690583059', '', 1, '2024-09-27 18:52:41', NULL, NULL, '1997-02-18', '18AN77513', '2018-07-30', 'ABYMES', NULL, 'LE GOSIER', 97190, '971', 'MANGO', 21, 21, 'Mr'),
(298, '[\"ROLE_CLIENT\"]', '$2y$13$J5Z043UrMjcLUmvyslqyeu5/Ym1/u3uDJY2GrtIzARmnt6nWgn.i.', 'MADINSKA', 'MARIE IGNACE', 'ROUTE DE LA HACHE MALESCO', '<EMAIL>', '0690963437', '', 1, '2024-09-28 14:50:17', NULL, NULL, '1972-07-31', '911096200288', '1993-04-23', 'MOULE', NULL, 'LE MOULE', 97160, 'POINTE A PITRE', 'MADINSKA', 23, 23, 'Mr'),
(300, '[\"ROLE_CLIENT\"]', '$2y$13$t0H9GSIThb.lG3posVozQu0t92ertcuYYAEJbonUSSeo0aYiPcTbK', 'rakoto', 'kapoka', 'tana', '<EMAIL>', '0325072183', '0325072183', 1, '2024-11-10 20:36:57', NULL, '$2y$13$t0H9GSIThb.lG3posVozQu0t92ertcuYYAEJbonUSSeo0aYiPcTbK', '1991-07-27', '123aer', '1991-07-27', 'tana', 'tana', 'tana', 101, 'tana', 'rakoto', NULL, NULL, 'masculin'),
(301, '[\"ROLE_CLIENT\"]', '$2y$13$/fbqJdAY/tommmyLuSn4dONZgkOrsGjkWzr7RKv0cA6Qgwwew4aR.', 'rakoto', 'yahoo', NULL, '<EMAIL>', '0325072183', NULL, 1, '2025-03-05 17:52:12', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'rakoto', NULL, NULL, NULL),
(302, '[\"ROLE_CLIENT\"]', '$2y$13$FxxSKxMiVYU8rio5hkyT6.2kVsblntvYBJGrLhJYDWVV51sd/Np7m', 'rija', 'benjamina', 'paris', '<EMAIL>', '0325072183', '0325072183', 1, '2025-04-24 16:50:20', NULL, '$2y$13$FxxSKxMiVYU8rio5hkyT6.2kVsblntvYBJGrLhJYDWVV51sd/Np7m', '2012-05-21', '12321', '2025-04-25', 'paris', 'test', 'paris', 10100, 'paris', 'rija', NULL, NULL, 'masculin'),
(305, '[\"ROLE_CLIENT\"]', '$2y$13$mnHvoLMFDe7XrXQzjjGS1e/18TeDjOAJXLA8cO19USUj3kL6ax/0y', 'mety sat', 'rija', 'test', '<EMAIL>', '0325072183', '0325072183', 0, '2025-04-24 21:12:47', NULL, NULL, '2020-12-12', '101223', '2000-12-12', 'test', 'test', 'test', 101000, 'test', 'mety sat', NULL, NULL, 'masculin'),
(306, '[\"ROLE_CLIENT\"]', '$2y$13$rfoaDmdRhMZidvMHntE82e5Tirj20nXmzxJNAtNFLjkoVIMNp7Vna', 'fsdfsd', 'fsfds', 'fsdfsdfsd', '<EMAIL>', '0325072183', '0325072183', 0, '2025-04-24 21:21:09', NULL, NULL, '2025-04-24', '101223', '2025-04-25', 'fsfsdf', 'ffsfsdfsd', 'fsdfsdf', 10100, 'fdsfsdf', 'fsdfsd', NULL, NULL, 'masculin'),
(307, '[\"ROLE_CLIENT\"]', '$2y$13$KHBLbMY.a8fglepD/VneHePvBeMU5uvuJ5LQFK.xba3htQATzeFJq', 'mety', 'mlrjelrj', 'fkdsmlk', '<EMAIL>', '0325072183', '0325072183', 0, '2025-04-25 17:09:05', NULL, NULL, '2020-12-12', '124520', '2020-02-12', 'test', 'kfmdskfmkm', 'rjljrlje', 101000, 'test', 'mety', NULL, NULL, 'masculin'),
(309, '[\"ROLE_CLIENT\"]', '$2y$13$mo6INlJu8oQ1DZtSkfv5Med347Ejwc9loEfd2YenZcpYBM0yrWOZi', 'mety sa tsy', 'ao ary', 'test', '<EMAIL>', '0325072183', '0325072183', 1, '2025-04-25 17:19:43', NULL, NULL, '2000-12-12', 'fdsfsdfs', '2020-12-12', 'test', 'sdfs', 'test', 10100, 'fsdfsdf', 'mety sa tsy', NULL, NULL, 'masculin'),
(310, '[\"ROLE_CLIENT\"]', '$2y$13$HGUc/koJe.ezTJqudGzmBeOeudEg/5v.Xos4/04Mq.NxbDw.0fMXy', 'mety sa tsy', 'ao ary', 'test', '<EMAIL>', '0325072183', '0325072183', 0, '2025-04-25 17:22:31', NULL, '22beafe0bb075e25c82685fdd81a5fe6dd57b6ba8fdaa74bb705c7e4d0744cdd', '2000-12-12', 'fdsfsdfs', '2020-12-12', 'test', 'sdfs', 'test', 10100, 'fsdfsdf', 'mety sa tsy', NULL, NULL, 'masculin'),
(311, '[\"ROLE_CLIENT\"]', '$2y$13$2uHBV.uY3I9wuksvN.eweOBBYn/EAN7J.Z6FazgjwFvZjhh9yGIzO', 'fsdfdsfd', 'fsdfdsfds', 'fsdfsdfsd', '<EMAIL>', '0325072183', '0325072183', 1, '2025-04-25 17:25:19', NULL, NULL, '2025-04-28', 'fsdfsdfsd', '2025-04-30', 'dsfsdf', 'fdsfsf', 'fsdfdsfdsfsd', 10100, 'fsdfsdf', 'fsdfdsfd', NULL, NULL, 'masculin'),
(312, '[\"ROLE_CLIENT\"]', '$2y$13$7cfZEOZyfNnwnJt2VvPEF.xRRA0imc0w2EQcNKc6ogw5VIWojoNSu', 'rakotoa', 'fjsdkfjls', 'fsfsd', '<EMAIL>', '0325072183', '0325072183', 1, '2025-04-27 20:17:44', NULL, NULL, '1200-12-19', '12000', '0002-02-17', 'dsfsfsd', 'fdsfsd', 'fsfsd', 10100, 'test', 'rakotoa', NULL, NULL, 'masculin'),
(313, '[\"ROLE_CLIENT\"]', '$2y$13$7hcf2rjZ4YZA1MhRX0J4QO2MwabOI1iX8fbQ4OqIqdk87hMn5Yi76', 'fjksdfjl', 'jkfljksdjflk', 'fsdfsd', '<EMAIL>', '0325072183', '0325072183', 0, '2025-04-27 20:22:35', NULL, 'a1781ced685028cf686f1cc516a5303031a039edb669a14bcfcfd2988682b1ec', '2020-12-12', '101223', '2020-12-12', 'fsdfsf', 'fdsfs', 'fdsfs', 10100, 'test', 'fjksdfjl', NULL, NULL, 'feminin'),
(315, '[\"ROLE_CLIENT\"]', '$2y$13$5uFpmm8EpnF.tD2Ka5tyy.jo7yoH//wRcu0mG.h877AQ2ZnlBDzoC', 'fjksdfjl', 'jkfljksdjflk', 'fsdfsd', '<EMAIL>', '0325072183', '0325072183', 1, '2025-04-27 20:23:59', NULL, NULL, '2020-12-12', '101223', '2020-12-12', 'fsdfsf', 'fdsfs', 'fdsfs', 10100, 'test', 'fjksdfjl', NULL, NULL, 'feminin'),
(317, '[\"ROLE_CLIENT\"]', '$2y$13$9/tQZGkFECDoCynI8fKcDOs5wbfQpMgunA5abb49TQi1nex6VsnqC', 'fjksdfjl', 'jkfljksdjflk', 'fsdfsd', '<EMAIL>', '0325072183', '0325072183', 1, '2025-04-27 20:28:28', NULL, NULL, '2020-12-12', '101223', '2020-12-12', 'fsdfsf', 'fdsfs', 'fdsfs', 10100, 'test', 'fjksdfjl', NULL, NULL, 'feminin');
INSERT INTO `user` (`id`, `roles`, `password`, `nom`, `prenom`, `adresse`, `mail`, `telephone`, `portable`, `presence`, `date_inscription`, `fonction`, `recupass`, `date_naissance`, `numero_permis`, `date_permis`, `lieu_naissance`, `complement_adresse`, `ville`, `code_postal`, `ville_delivrance_permis`, `username`, `infos_resa_id`, `infos_vol_resa_id`, `sexe`) VALUES
(318, '[\"ROLE_CLIENT\"]', '$2y$13$m4V3a1f7G/W/zCgBJdxa.u.o.s01TvTrFH8x7dS5o9o0Rk7bLnrua', 'fjksdfjl', 'jkfljksdjflk', 'fsdfsd', '<EMAIL>', '0325072183', '0325072183', 0, '2025-04-27 20:39:00', NULL, NULL, '2020-12-12', '101223', '2020-12-12', 'fsdfsf', 'fdsfs', 'fdsfs', 10100, 'test', 'fjksdfjl', NULL, NULL, 'feminin'),
(320, '[\"ROLE_CLIENT\"]', '$2y$13$FwqDJ370u4jxjRQeexXd4Osqh9IrF6GFB9xSXMRIFWTOh38mFiXlm', 'rkdlsjflsj', 'ljflkj', 'fsdfsd', '<EMAIL>', '0325072183', '0325072183', 1, '2025-05-01 05:24:20', NULL, '', '2000-12-12', '1231313', '0002-12-10', 'test', 'fdsfsd', 'fsdfsd', 10100, 'fsdfsdf', 'rkdlsjflsj', NULL, NULL, 'masculin'),
(321, '[\"ROLE_CLIENT\"]', '$2y$13$TJqbzXDYvpZD8wXiA9VXg.CS5paZedJLBxIdpuroZw2YgYXIePlWC', 'fjksldjfk', 'kfjsdlkfjl', 'fsfsdf', '<EMAIL>', '0325072183', '0325072183', 0, '2025-05-01 08:42:24', NULL, '59a74f727da69715d441148dbef66ad937cfcf07ff39488e594d1fce0e6212fc', '2025-12-12', '101223', '2025-04-28', 'fsdfsd', 'fsfs', 'fsfsdf', 10100, 'gvdfgdf', 'fjksldjfk', NULL, NULL, 'masculin'),
(322, '[\"ROLE_CLIENT\"]', '$2y$13$7f97w0xX3tsLNhHJ51xvb.eTxy7KC.KoDqjT0r.7KLnjGIHIdotrC', 'fsfsd', 'fsfsd', 'fsfsdf', '<EMAIL>', '0325072183', '0325072183', 0, '2025-05-01 08:52:21', NULL, '9cbf610845a0f1107ecf16a608928cec16c6fb8a9cd3eb3816725ecb50aa0b3a', '2025-04-28', '12000', '2025-04-28', 'fsfds', 'fdsfsdf', 'fsfsd', 10100, 'fdsfsdf', 'fsfsd', NULL, NULL, 'masculin'),
(323, '[\"ROLE_CLIENT\"]', '$2y$13$BtZZQ0U64Gzl7AKh3IxSxOfofxgdt56hDiJryHsMq4RreEe9t8PTa', 'gdgfdgdfg', 'gfdgdfgdfg', 'gdfgdf', '<EMAIL>', '0325072183', '0325072183', 0, '2025-05-01 08:57:42', NULL, '2d42383752edb5dd3bcb94a1bdbef4f135cb1d38dfd348be2875d92777d96dc9', '2025-04-28', '101223', '2025-04-29', 'gdgdf', 'gdfgdf', 'gdfgdf', 10100, 'test', 'gdgfdgdfg', NULL, NULL, 'masculin'),
(324, '[\"ROLE_CLIENT\"]', '$2y$13$4wLrSDxW1dl2tySzzox73eFc9C63sXRKP2zigzKPFJw0i4w5B8TG6', 'fsfsd', 'fsdfs', 'fsdfsdf', '<EMAIL>', '0325072183', '0325072183', 0, '2025-05-01 09:05:25', NULL, '1a3bad32c7e0ae7895369f1fa7ef0bd8a58325841b9476bc5a86e55a0139f322', '2025-04-28', '101223', '2025-04-28', 'fsfsdf', 'fsdfsdf', 'fsdfsdf', 10100, 'dsfgfdgd', 'fsfsd', NULL, NULL, 'masculin'),
(325, '[\"ROLE_CLIENT\"]', '$2y$13$l92swQYdeWD9Pv7YW1lLleCbzSJnjT7v8zafYobz1QxN5o4ZCVF7C', 'fsfsd', 'fsfsd', 'fsdfsdf', '<EMAIL>', '0325072183', '0325072183', 1, '2025-05-01 09:09:33', NULL, NULL, '2025-04-28', '101223', '2025-04-01', 'fsdfsd', 'fsfsdf', 'fsdfdsf', 10100, 'fsdfsdf', 'fsfsd', NULL, NULL, 'masculin'),
(326, '[\"ROLE_CLIENT\"]', '$2y$13$zKG5TBJ7bh0gMDnhbR1eLe2kHRO4taPNHZLwBncC0d5RaIgEruGZe', 'fsdfsdf', 'fsdfsdf', 'fsdfsd', '<EMAIL>', '0325072183', '0325072183', 0, '2025-05-01 09:23:19', NULL, '37e98b6814b016a608246c3a610fef4215eeaaa1812c6fcb365ba8b9b239c8db', '2025-03-31', '101223', '2025-05-05', 'fsdfsd', 'fsdfsdf', 'fsdfsdf', 101000, 'jljkljkl', 'fsdfsdf', NULL, NULL, 'masculin'),
(327, '[\"ROLE_CLIENT\"]', '$2y$13$CrU./sswkrEl8WC6zjrzm.EN2zyBkYxGPI31Omgy8gwZIv1RlP2wy', 'fsdfsdf', 'fsdfsdfsd', 'fsfsd', '<EMAIL>', '0325072183', '0325072183', 0, '2025-05-01 06:41:25', NULL, '64ad9b6b864d832da536a8fb251d38a82ffd2ea86a4a49f210fd867c1e587191', '2025-02-25', '101223', '2025-04-01', 'fsfsd', 'fsdfs', 'fsdfsd', 10100, 'gdfgdfg', 'fsdfsdf', NULL, NULL, 'masculin'),
(328, '[\"ROLE_CLIENT\"]', '$2y$13$GEfUKhfkG4BANpQnXtjZ6.3vJdIsJq4kOEN4v9rGprtDHN2yS.gO2', 'fsdfsd', 'fsdfsd', 'fsfsd', '<EMAIL>', '0325072183', '0325072183', 1, '2025-05-01 09:44:58', NULL, NULL, '2025-04-01', '101223', '2025-04-28', 'fsdfsd', 'fdsfsdf', 'fsdfsdf', 10100, 'fdsfsdf', 'fsdfsd', NULL, NULL, 'masculin'),
(329, '[\"ROLE_CLIENT\"]', '$2y$13$HhFBq.DlGcXP4/nFR2fSDuk.OqIIr0Auw0ffzhL/Xt4xQAhM2cTKu', 'fsdfsdf', 'fsfsd', 'fsdfsd', '<EMAIL>', '0325072183', '0325072183', 0, '2025-05-01 10:00:57', NULL, '3fba51c842ea25dacfcc1dac360ff4f421492fa95a2361f189826f57e4847d31', '2025-04-28', '101223', '2025-02-24', 'fsdfsdf', 'fsdfsd', 'fsdf', 10100, 'gdfgdfgd', 'fsdfsdf', NULL, NULL, 'masculin'),
(330, '[\"ROLE_CLIENT\"]', '$2y$13$XrVaNRYfkMxKLMvW5qKtyeQ/OrfAZ59AJIon3mdm3cSoyJeYVDsGS', 'fsdfsd', 'fsdfsd', 'fsfsdf', '<EMAIL>', '0325072183', '0325072183', 0, '2025-05-01 10:02:49', NULL, '18b1fbe147869745d57fb762ed649ed4e986a294fd2ef3d1dc9e8bb7ff93800d', '2025-02-24', '101223', '2025-02-24', 'fsdfsdfsd', 'ffsfsd', 'fsdfsdf', 10100, 'gdfgdfgdf', 'fsdfsd', NULL, NULL, 'masculin'),
(331, '[\"ROLE_CLIENT\"]', '$2y$13$t9xZlWQ1R8ExI5S2bgaaKeDsWxVR5rAxJy.8UnmrD1vCSPowVKxH6', 'fsdfsd', 'fsdfsd', 'fsdfsd', '<EMAIL>', '0325072183', '0325072183', 0, '2025-05-01 10:14:15', NULL, '43297e14ec3bcf79ca0328e6e028805df87c1b313c26b744d35cab7763fec603', '2025-04-28', '101223', '2025-04-28', 'fsdfs', 'fsdfsdf', 'fsdfsdf', 10100, 'gdfgdfgdf', 'fsdfsd', NULL, NULL, 'masculin'),
(332, '[\"ROLE_CLIENT\"]', '$2y$13$55ZM.iNzW7BeVLnfW3KcIeHzEX8YhblQFpTD2LNGKAPhTTbKCR4UK', 'fsdlmfklm', 'kfmlksdmlfklmskml', 'fdsfsdfsd', '<EMAIL>', '0325072183', '0325072183', 1, '2025-05-01 10:16:55', NULL, NULL, '2025-04-01', '101223', '2025-02-03', 'fsdfsd', 'fsdfsd', 'fsdfsdf', 10100, 'ngfhfgh', 'fsdlmfklm', NULL, NULL, 'masculin'),
(333, '[\"ROLE_CLIENT\"]', '$2y$13$VT/5GEemqwy.ZEdEdzVmgOqLEODItcsfns0wGJUEo.lCB6.MaIIqK', 'fsfsdfsd', 'fsdfsdf', 'fsdfsdf', '<EMAIL>', '0325072183', '0325072183', 0, '2025-05-01 10:21:02', NULL, '7e5de327f07179161cf182e9702df886d0ae75261b4c030ecc1d68da0469ff7f', '2025-04-28', '101223', '2025-04-01', 'fsdfsdf', 'fsdfsdf', 'ffsdfsd', 10100, 'hfghfghf', 'fsfsdfsd', NULL, NULL, 'masculin'),
(334, '[\"ROLE_CLIENT\"]', '$2y$13$mo6INlJu8oQ1DZtSkfv5Med347Ejwc9loEfd2YenZcpYBM0yrWOZi', 'fsdfsdf', 'fsdfsd', 'fsdfsd', '<EMAIL>', '0325072183', '0325072183', 0, '2025-05-01 10:24:06', NULL, 'e132575cb8af649c7466e6d8ac1f85a20ed113231e2fa84e09a3d0c46df7e7ea', '2025-04-29', '101223', '2025-04-28', 'fsfsd', 'fdsfsd', 'fsfsdf', 10100, 'gfdgd', 'fsdfsdf', NULL, NULL, 'masculin'),
(335, '[\"ROLE_CLIENT\"]', '$2y$13$2QogYAXQoDG8jEih47KYBuqBzlPA4gbq7aqBVjGAe5h8qKHN35c7m', 'vxdfsdfs', 'fsdfsdfsd', 'fsdfs', '<EMAIL>', '0325072183', '0325072183', 1, '2025-05-01 09:25:09', NULL, NULL, '2025-04-01', '101223', '2025-05-05', 'fdsfsd', 'fsdfs', 'fsfsd', 10100, 'hfghfg', 'vxdfsdfs', NULL, NULL, 'masculin'),
(336, '[\"ROLE_CLIENT\"]', '$2y$13$KhtF9jAc2IXEAg2K8HU4GO1HHGc72yebma5k6Lfu2AxZtM.62iR5u', 'fsdfsd', 'fsdfsd', 'fdsfsd', '<EMAIL>', '0325072183', '0325072183', 0, '2025-05-01 12:50:48', NULL, 'ad7f556983e68a787a90d40230846c19ee4e080f9539112c01ca33a2509c9620', '2025-04-28', '101223', '2025-02-25', 'fdsfs', 'fsdfsd', 'fsdfsdf', 10100, 'fsdfsdf', 'fsdfsd', NULL, NULL, 'masculin'),
(337, '[\"ROLE_CLIENT\"]', '$2y$13$YBLbgWPcDhCDws1rw50TeO/SwA4jKa75ZwwOnO9oYFgJrGOyhi32y', 'sdfsdfsdfsdffsf', 'ffsfsdffsdf', 'fsdfsdf', '<EMAIL>', '0325072183', '0325072183', 0, '2025-05-01 12:54:24', NULL, 'bf945ee0774d178bdd8df8e339617f8123a0b025068b40c0fe5b7fa150cebba5', '2024-10-05', '101223', '2025-03-05', 'fsdfsdfsdf', 'fsdfsd', 'fsdfsdfs', 10100, 'gdfgdf', 'sdfsdfsdfsdffsf', NULL, NULL, 'masculin'),
(338, '[\"ROLE_CLIENT\"]', '$2y$13$.wL/PIue3ioy9DiJnPgIFeejatoZjM/LDzfJMb1s5yA0D2YrMr2Zi', 'fsdfsdf', 'fsdfsdf', 'fsfsd', '<EMAIL>', '0325072183', '0325072183', 1, '2025-05-01 09:56:56', NULL, NULL, '2025-03-01', '101223', '2025-01-27', 'fdfsd', 'fsdfsd', 'fsdfsdf', 10100, 'fsdfsdf', 'fsdfsdf', NULL, NULL, 'masculin'),
(339, '[\"ROLE_CLIENT\"]', '$2y$13$/X/z4.owzYNX6pnhkpzMBOlRiQlUxtdsLDmtknKcP4NTI/z0mDMgS', 'gdgdfg', 'gdgdfg', 'gdgdf', '<EMAIL>', '0325072183', '0325072183', 0, '2025-05-01 13:02:38', NULL, 'c02839ab4ee023cd95787e01ca0fc687b1c7b134f6ccd4586672dccc58693872', '2025-01-28', '101223', '2025-02-24', 'gdfgdfg', 'gdfgdf', 'gdgdfg', 10100, 'hfghfg', 'gdgdfg', NULL, NULL, 'masculin'),
(340, '[\"ROLE_CLIENT\"]', '$2y$13$S6wJ486CC4SsnJGtXJY7YO9xi9ArABCJcL0QSAiMDcTvj.r1LaSL2', 'ffsd', 'fsdfsdf', 'fsfsdf', '<EMAIL>', '0325072183', '0325072183', 0, '2025-05-01 13:05:52', NULL, '316312b14a87b87d71d06ca2c3b5b19e1d80ca4be73319baf6ff4a970154db69', '2025-02-25', '101223', '2025-04-28', 'fsdfsdf', 'fsfsd', 'fsfsdf', 10100, 'fdsfsdfsd', 'ffsd', NULL, NULL, 'masculin'),
(341, '[\"ROLE_CLIENT\"]', '$2y$13$xeVVSjrLNRPgw9J3NlrVk.s5EQqvNd272mZY/GRf5niYAWYoow6Yq', 'fsdfsdf', 'fsdfsd', 'fsfsd', '<EMAIL>', '0325072183', '0325072183', 0, '2025-05-01 13:13:16', NULL, '2f1b405e8e63e3d894de9b723b53787e044774b9b672ce4f9ef765037b40fcfb', '2025-03-31', '12000', '2025-04-28', 'fdsfsd', 'ffsdfsdf', 'fsdfsdf', 10100, 'gdfgdfgdf', 'fsdfsdf', NULL, NULL, 'masculin'),
(343, '[\"ROLE_CLIENT\"]', '$2y$13$34IboyfGomjDRXtev7S8sOmv4H/9SljsL1JPbske69a.fKpiiZz3.', 'fdgdf', 'gdfg', 'gdfgd', '<EMAIL>', '0325072183', '0325072183', 1, '2025-05-05 20:01:07', NULL, NULL, '2025-02-03', '101223', '2024-12-30', 'gfdgdf', 'fdgdfgd', 'gdfgdfg', 10100, 'gbfdgdf', 'fdgdf', NULL, NULL, 'masculin'),
(344, '[\"ROLE_CLIENT\"]', '$2y$13$W/qH9REtQ.ALLW4tpwVsReYHL4U3w163wk391OXrb2mZRD1UkiXCK', 'dgdfg', 'gdfgdfg', 'fsdfsdf', '<EMAIL>', '0325072183', '0325072183', 1, '2025-05-05 20:13:50', NULL, NULL, '2024-11-25', '101223', '2025-01-27', 'dgdfgdf', 'fsfsdfsd', 'fsdfsdfsd', 10100, 'gddfgdfgdf', 'dgdfg', NULL, NULL, 'masculin'),
(345, '[\"ROLE_CLIENT\"]', '$2y$13$xOgZSvBYgK4wkZgn7D4pVOqNRty4qHq1kRNa1IT30DAr86TwQqY9G', 'dvdgdf', 'gdfgdfgdfg', 'hfghfgh', '<EMAIL>', '0325072183', '0325072183', 0, '2025-05-05 20:17:19', NULL, '0a036c6f590a1bc13fc55b2242fc3a63990b4f263540b35a4dcd5092d4f2cc10', '2025-01-01', '101223', '2024-12-30', 'nghgfg', 'hfghfg', 'hfghfgh', 10100, 'hfghfhf', 'dvdgdf', NULL, NULL, 'masculin'),
(346, '[\"ROLE_CLIENT\"]', '$2y$13$xHP0eo75/FNX/4isgqR6buCPbaEi8oUbhX244qG8k8BsZpwP3AD5e', 'fsdfsd', 'fsdfsdf', 'fdsfs', '<EMAIL>', '0325072183', '0325072183', 0, '2025-05-05 21:03:53', NULL, 'a11eefa2298c3db17e979135d6aed7b071db4779b8cd1e008da356f426b0571a', '2025-01-27', 'hfghfgh', '2025-04-01', 'fsdfsd', 'fsdfsd', 'fsdfsd', 10100, 'hfghfgh', 'fsdfsd', NULL, NULL, 'masculin'),
(348, '[\"ROLE_CLIENT\"]', '$2y$13$aB7JWuv4ZrOFJphr.UAJa.F6rGdtnRe8MzIrgnmrZrFbGc2WZ9uSe', 'gdfgdg', 'gdgdf', 'gdgdf', '<EMAIL> ', '0325072183', '0325072183', 0, '2025-05-06 18:09:22', NULL, '732096e886da28836d633369bed24aa516b84cb3e9823ce8d69447541510aa30', '2025-03-05', '101223', '2025-02-04', 'gdfgdf', 'gdfgdf', 'gdfgdfg', 101000, 'gdfgdf', 'gdfgdg', NULL, NULL, 'masculin'),
(349, '[\"ROLE_CLIENT\"]', '$2y$13$uZ198jzaVEO.1EVDxMEJvOxf.DFayCLeFulFOrpyYbvVy3TZv4OYC', 'jdsklfjsdjflk', 'jlkfjsdlkfjlkj', 'fsdfsdf', '<EMAIL>', '0325072183', '0325072183', 1, '2025-05-08 14:29:09', NULL, NULL, '2024-12-30', '101223', '2025-03-04', 'testerst', 'fsdfsdf', 'fsfds', 10100, 'gddfgdfg', 'jdsklfjsdjflk', NULL, NULL, 'masculin'),
(350, '[\"ROLE_CLIENT\"]', '$2y$13$QWnzhGOKUFBIZ/GffWHr7.MYzGeml13caY60WKlE4Z2uHzQnKP9YG', 'fsfsdf', 'fsdfs', 'fsdfsd', '<EMAIL>', '0325072183', '0325072183', 0, '2025-05-08 15:00:48', NULL, '7ef5d4704ee086a303b6bc1581d4969beb015c39992005e34bc12933881104f9', '2025-05-09', 'fsqfsdf', '2025-05-09', 'fsdfsd', 'fsdfsdf', 'fsfsf', 10100, 'fsdfsq', 'fsfsdf', NULL, NULL, 'masculin'),
(351, '[\"ROLE_CLIENT\"]', '$2y$13$007yWidbue/1eX/I.2EsHuWgRqUHZdaazEVr5PP1tvg5LGLaHUHqi', 'dfgdfkljaljdfklg', 'ljfkljsdfgjdflkjl', 'fsdfsd', '<EMAIL>', '0325072183', '0325072183', 0, '2025-05-08 15:34:49', NULL, 'fce3eeb721cdddf2c8f7c41fedc058d5a9953d67d7d3cd472806dd4ce07b39e5', '2025-05-12', 'fsfsd', '2025-03-03', 'fsdfsd', 'fsdfsd', 'fsdfsdf', 10100, 'ffsdfsd', 'dfgdfkljaljdfklg', NULL, NULL, 'masculin'),
(352, '[\"ROLE_CLIENT\"]', '$2y$13$Dgcg41MJzCksnqTr5XS97uP6bmwcxCW01bKsHz9pDcFPkiN1VGoG6', 'fsfs', 'fsfsd', 'fsfds', '<EMAIL>', '0325072183', '0325072183', 1, '2025-05-08 15:36:47', NULL, NULL, '2025-05-09', '101223', '2024-10-28', 'fsfsdf', 'fsfsd', 'fsdfsd', 10100, 'fsdfsd', 'fsfs', NULL, NULL, 'masculin'),
(353, '[\"ROLE_CLIENT\"]', '$2y$13$A1Wf5rBlAPB4M8KSyF2FQ.pRoHZyI72REfRjyMYCVknXIrM4do2Zm', 'fsmdfjkdklsjlk', 'jflksjdfklj', 'fsdfs', '<EMAIL>', '0325072183', '0325072183', 0, '2025-05-08 15:45:27', NULL, '80289b936d1b84f8efb809d0daf2db3e5c2062da9fa1e36e354b4a4c1caabb73', '0202-05-12', '101223', '2025-02-03', 'fsdf', 'fsdfsd', 'fsdfsd', 10100, 'fsdfs', 'fsmdfjkdklsjlk', NULL, NULL, 'masculin'),
(354, '[\"ROLE_CLIENT\"]', '$2y$13$8YjIPGw4MJFX3mv/isWFxesE7kYQY3S9E0SymL6Ng3N7Lh1udIXHi', 'Dupont', 'Jean', '123 Rue Test', '<EMAIL>', '0123456789', '0612345678', 0, '2025-05-08 15:54:06', NULL, '423146c0ba68041fe07ecf16935cb16010afe9f444a8e25aaf01bda2cdcd4530', '1990-01-01', '12345678', '2010-01-01', 'Paris', 'Apt 42', 'Paris', 75001, 'Paris', 'Dupont', NULL, NULL, 'masculin'),
(355, '[\"ROLE_CLIENT\"]', '$2y$13$Uq/bbjQowcKS6DmwBqHK9OrrjrF2nEpT8hrtypot3wAd8ZNQLa9Vu', 'Dupont', 'Jean', '123 Rue Test', '<EMAIL>', '0123456789', '0612345678', 0, '2025-05-08 15:55:11', NULL, '2de23658be3fef840cc2b3dae520c8dabcae84e836f363cc8290f6f948f16729', '1990-01-01', '12345678', '2010-01-01', 'Paris', 'Apt 42', 'Paris', 75001, 'Paris', 'Dupont', NULL, NULL, 'masculin'),
(356, '[\"ROLE_CLIENT\"]', '$2y$13$t9FHdDfNwXIrdoSSTqxFwuMJzb5u6/sUox4X.YuL3cvRioaYdQ.jK', 'Dupont', 'Jean', '123 Rue Test', '<EMAIL>', '0123456789', '0612345678', 0, '2025-05-08 17:55:48', NULL, '57eb35e6fa50fa12c31c0f67e6dfd5cadfa86a38011224740947e2396575c78d', '1990-01-01', '12345678', '2010-01-01', 'Paris', 'Apt 42', 'Paris', 75001, 'Paris', 'Dupont', NULL, NULL, 'masculin'),
(357, '[\"ROLE_CLIENT\"]', '$2y$13$Xau5rJyWWWbjiwcHyDIv.OlzGXHQ/sz1W//trjS.yV6JaALhRhhDG', 'Dupont', 'Jean', '123 Rue Test', '<EMAIL>', '0123456789', '0612345678', 0, '2025-05-08 17:57:12', NULL, '392068eac73f007d4b2c7748bf5642d6dfb879efea10eda3dd391e4b42be8adf', '1990-01-01', '12345678', '2010-01-01', 'Paris', 'Apt 42', 'Paris', 75001, 'Paris', 'Dupont', NULL, NULL, 'masculin'),
(358, '[\"ROLE_CLIENT\"]', '$2y$13$BQkHfW0c/GW4L.sj3detse..syalqBgyeDOTH01GSzhsXHrEoV8dK', 'Dupont', 'Jean', '123 Rue Test', '<EMAIL>', '0123456789', '0612345678', 0, '2025-05-08 18:00:18', NULL, '2a8271aca820196cb328257210d6dc4abd995b915ee5bb8195d2a0e2b853e324', '1990-01-01', '12345678', '2010-01-01', 'Paris', 'Apt 42', 'Paris', 75001, 'Paris', 'Dupont', NULL, NULL, 'masculin'),
(359, '[\"ROLE_CLIENT\"]', '$2y$13$GQVxEMMUf1JveBBc0lQbcetlxs9IIaqX7uQcmDDzGONOnd.2BkQHy', 'Dupont', 'Jean', '123 Rue Test', '<EMAIL>', '0123456789', '0612345678', 0, '2025-05-08 18:03:33', NULL, '60af156f42abbc431cb2c6e47bc289dfca6e08bc61478723e23539cdf802fef4', '1990-01-01', '12345678', '2010-01-01', 'Paris', 'Apt 42', 'Paris', 75001, 'Paris', 'Dupont', NULL, NULL, 'masculin'),
(360, '[\"ROLE_CLIENT\"]', '$2y$13$EcWep..0Esif2yuMjTL95ugljHUW5F3glQhUOkePbvHlZ1SIXoG4e', 'Dupont', 'Jean', '123 Rue Test', '<EMAIL>', '0123456789', '0612345678', 0, '2025-05-08 18:05:15', NULL, 'eb3fb1e2204f52c5bff883a89bae454384ec62c34c3d6a001aa41b07a1a1ff54', '1990-01-01', '12345678', '2010-01-01', 'Paris', 'Apt 42', 'Paris', 75001, 'Paris', 'Dupont', NULL, NULL, 'masculin'),
(361, '[\"ROLE_CLIENT\"]', '$2y$13$nIlvGC//O9JLBCBEDISVxukehiDab4vQz4s3/G6RbhWyFs5fRltty', 'Dupont', 'Jean', '123 Rue Test', '<EMAIL>', '0123456789', '0612345678', 0, '2025-05-08 18:13:55', NULL, 'ef4fb18e6db833924105895478fb602062f9e511156d82bfa2703fe0b0a462e5', '1990-01-01', '12345678', '2010-01-01', 'Paris', 'Apt 42', 'Paris', 75001, 'Paris', 'Dupont', NULL, NULL, 'masculin'),
(362, '[\"ROLE_CLIENT\"]', '$2y$13$QfXnaDFQxIowNUeg.C5L7.t8Lh0atqMsNjnkNsNCD9srKm1cPyVFu', 'Dupont', 'Jean', '123 Rue Test', '<EMAIL>', '0123456789', '0612345678', 0, '2025-05-08 18:15:14', NULL, '2001df4fb4d0edf247f6279b2c69691c2c823e393647e8a14cd5ea5404b5a92f', '1990-01-01', '12345678', '2010-01-01', 'Paris', 'Apt 42', 'Paris', 75001, 'Paris', 'Dupont', NULL, NULL, 'masculin'),
(363, '[\"ROLE_CLIENT\"]', '$2y$13$shUtbY3fHlWw.oZHS5b7i.9Ijj5GbSpNVG5a7gI31wZswLVqPPHiy', 'Dupont', 'Jean', '123 Rue Test', '<EMAIL>', '0123456789', '0612345678', 1, '2025-05-08 18:21:00', NULL, NULL, '1990-01-01', '12345678', '2010-01-01', 'Paris', 'Apt 42', 'Paris', 75001, 'Paris', 'Dupont', NULL, NULL, 'masculin'),
(364, '[\"ROLE_CLIENT\"]', '$2y$13$yh6q4w5v5V0uSqgqzFnpKeqFC9ZbalmarASvrXZddXZuPSZUQ0AYC', 'Dupont', 'Jean', '123 Rue Test', '<EMAIL>', '0123456789', '0612345678', 1, '2025-05-08 18:23:55', NULL, NULL, '1990-01-01', '12345678', '2010-01-01', 'Paris', 'Apt 42', 'Paris', 75001, 'Paris', 'Dupont', NULL, NULL, 'masculin'),
(365, '[\"ROLE_CLIENT\"]', '$2y$13$cYrc/i/pOwbTqCL7P8KyAeonFtNoASBJqcEo6N29MuP1F2DFOPwia', 'Dupont', 'Jean', '123 Rue Test', 'mety <EMAIL>', '0123456789', '0612345678', 1, '2025-05-08 18:40:29', NULL, NULL, '1990-01-01', '12345678', '2010-01-01', 'Paris', 'Apt 42', 'Paris', 75001, 'Paris', 'Dupont', NULL, NULL, 'masculin'),
(366, '[\"ROLE_CLIENT\"]', '$2y$13$4RxSH0HAHF1RPYemhZsPtOpO48GcL8F/K56Q3fMPqzhnkz9UNhe92', 'Dupont', 'Jean', '123 Rue Test', '<EMAIL>', '0123456789', '0612345678', 1, '2025-05-08 18:45:58', NULL, NULL, '1990-01-01', '12345678', '2010-01-01', 'Paris', 'Apt 42', 'Paris', 75001, 'Paris', 'Dupont', NULL, NULL, 'masculin'),
(367, '[\"ROLE_CLIENT\"]', '$2y$13$WNY61Tc8zrBjIsVTaEXe3eBI6p1hLZdVO36P4fz9cFfTVnulQBLoe', 'Dupont', 'Jean', '123 Rue Test', '<EMAIL>', '0123456789', '0612345678', 1, '2025-05-08 19:56:17', NULL, NULL, '1990-01-01', '12345678', '2010-01-01', 'Paris', 'Apt 42', 'Paris', 75001, 'Paris', 'Dupont', NULL, NULL, 'masculin'),
(368, '[\"ROLE_CLIENT\"]', '$2y$13$X/ECqkmjUmR0vB/1JQQLE.uWdujuyMK4chuEw6Id3D44.dVawm63W', 'Dupont', 'Jean', '123 Rue Test', '<EMAIL>', '0123456789', '0612345678', 0, '2025-05-08 20:03:28', NULL, 'c3a224e2c9e8e7242e1a8e16380ba5119ce29582c0e2816aeb31524eb15036fu', '1990-01-01', '12345678', '2010-01-01', 'Paris', 'Apt 42', 'Paris', 75001, 'Paris', 'Dupont', NULL, NULL, 'masculin'),
(369, '[\"ROLE_CLIENT\"]', '$2y$13$yDWTyRphTaUU1pLiGXtuHejvxxOowjSGUDvd6dQjOeusoQ1FFd7fe', 'Dupont', 'Jean', '123 Rue Test', '<EMAIL>', '0123456789', '0612345678', 1, '2025-05-09 07:00:54', NULL, NULL, '1990-01-01', '12345678', '2010-01-01', 'Paris', 'Apt 42', 'Paris', 75001, 'Paris', 'Dupont', NULL, NULL, 'masculin'),
(370, '[\"ROLE_CLIENT\"]', '$2y$13$EyO/tkYlqutedp41FRcM9.iPb2vyIeRlpUQvM5hfoZx0udeh4aOrW', 'Dupont', 'Jean', '123 Rue Test', '<EMAIL>', '0123456789', '0612345678', 1, '2025-05-09 07:38:21', NULL, NULL, '1990-01-01', '12345678', '2010-01-01', 'Paris', 'Apt 42', 'Paris', 75001, 'Paris', 'Dupont', NULL, NULL, 'masculin'),
(371, '[\"ROLE_CLIENT\"]', '$2y$13$ive47LT7zMvKWNDR9sUITOPAvFZXWuDwrrxYzkH2WmiS7F6Xq3Alm', 'Dupont', 'Jean', '123 Rue Test', '<EMAIL>', '0123456789', '0612345678', 0, '2025-05-09 12:20:39', NULL, 'f3a635ed08732420d1edf01a42d4427acefb57e236963574675324b78887d6d6', '1990-01-01', '12345678', '2010-01-01', 'Paris', 'Apt 42', 'Paris', 75001, 'Paris', 'Dupont', NULL, NULL, 'masculin'),
(372, '[\"ROLE_CLIENT\"]', '$2y$13$llGsetw/N3cPJ2fGgCgsXeqBjyKaLk6OVQQQWsKeOko8pzB1DvFtC', 'Dupont', 'Jean', '123 Rue Test', '<EMAIL>', '0123456789', '0612345678', 1, '2025-05-09 12:34:07', NULL, NULL, '1990-01-01', '12345678', '2010-01-01', 'Paris', 'Apt 42', 'Paris', 75001, 'Paris', 'Dupont', NULL, NULL, 'masculin'),
(373, '[\"ROLE_CLIENT\"]', '$2y$13$kedmGb0eFtqxjQw3D6.bhOZg6Iekj1A/zwH..XrHh1X8vMTqk32Ti', 'Dupont', 'Jean', '123 Rue Test', '<EMAIL>', '0123456789', '0612345678', 1, '2025-05-09 09:43:15', NULL, NULL, '1990-01-01', '12345678', '2010-01-01', 'Paris', 'Apt 42', 'Paris', 75001, 'Paris', 'Dupont', NULL, NULL, 'masculin'),
(374, '[\"ROLE_CLIENT\"]', '$2y$13$HRCzvCOvQ7lRsMVvnBoNEuszSNdncjGCZ/w.CeWhhxdRvk3PwmHW2', 'fsdfk', 'ljfksjl', 'fsfds', '<EMAIL>', '0325072183', '0325072183', 0, '2025-05-11 22:05:52', NULL, 'e2f90f820e290a39c07388ab59ae10f1a0d9717c2c0b30688ebb75db256163d9', '2020-12-12', '101223', '2024-12-30', 'test', 'fsfsd', 'fsfsd', 10100, 'fsdfsdf', 'fsdfk', NULL, NULL, 'feminin'),
(375, '[\"ROLE_CLIENT\"]', '$2y$13$XbQqUOSUIo226FSY2MjlPenGI9greLodtM6l3ZVArVYpCoLUqKlzC', 'Dupont', 'Jean', '123 Rue Test', '<EMAIL>', '0123456789', '0612345678', 1, '2025-05-12 19:16:28', NULL, NULL, '1990-01-01', '12345678', '2010-01-01', 'Paris', 'Apt 42', 'Paris', 10100, 'Paris', 'Dupont', NULL, NULL, 'masculin'),
(376, '[\"ROLE_CLIENT\"]', '$2y$13$ZkHpqDe9vpKYFcCHZtjCg.8gTzs5NwKavL3CoYeNnAtksktchMury', 'Dupont', 'Jean', '123 Rue Test', '<EMAIL>', '0123456789', '0612345678', 1, '2025-05-12 20:08:57', NULL, NULL, '1990-01-01', '12345678', '2010-01-01', 'Paris', 'Apt 42', 'Paris', 75001, 'Paris', 'Dupont', NULL, NULL, 'masculin'),
(378, '[\"ROLE_CLIENT\"]', '$2y$13$viY9GLMdKUwWrIcVKPuLcu2FxORvookObZCgjw.ZLLf3pp9pG2WOW', 'Dupont', 'Jean', '123 Rue Test', '<EMAIL>', '0123456789', '0612345678', 1, '2025-05-20 17:21:12', NULL, NULL, '1990-01-01', '12345678', '2010-01-01', 'Paris', 'Apt 42', 'Paris', 75001, 'Paris', 'Dupont', NULL, NULL, 'masculin');

-- --------------------------------------------------------

--
-- Structure de la table `vehicule`
--

DROP TABLE IF EXISTS `vehicule`;
CREATE TABLE IF NOT EXISTS `vehicule` (
  `id` int NOT NULL AUTO_INCREMENT,
  `type_id` int DEFAULT NULL,
  `immatriculation` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `date_mise_service` date DEFAULT NULL,
  `date_mise_location` date NOT NULL,
  `prix_acquisition` int DEFAULT NULL,
  `details` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `carburation` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `caution` double DEFAULT NULL,
  `vitesse` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `bagages` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `portes` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `passagers` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `atouts` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `modele_id` int DEFAULT NULL,
  `marque_id` int DEFAULT NULL,
  `km_depart` double DEFAULT NULL,
  `km_retour` double DEFAULT NULL,
  `date_km` datetime DEFAULT NULL,
  `saisisseur_km_id` int DEFAULT NULL,
  `options` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '(DC2Type:json)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `UNIQ_292FFF1DC1E5BBF9` (`saisisseur_km_id`),
  KEY `IDX_292FFF1DC54C8C93` (`type_id`),
  KEY `IDX_292FFF1DAC14B70A` (`modele_id`),
  KEY `IDX_292FFF1D4827B9B2` (`marque_id`)
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `vehicule`
--

INSERT INTO `vehicule` (`id`, `type_id`, `immatriculation`, `date_mise_service`, `date_mise_location`, `prix_acquisition`, `details`, `carburation`, `caution`, `vitesse`, `bagages`, `portes`, `passagers`, `atouts`, `image`, `updated_at`, `modele_id`, `marque_id`, `km_depart`, `km_retour`, `date_km`, `saisisseur_km_id`, `options`) VALUES
(1, 1, 'CT-547-ZF', '2013-06-01', '2022-07-08', 8200, 'Avec le Renault Captur, vous pouvez parcourir des kilomètres en Guadeloupe. Cette voiture est idéale pour tous vos déplacements touristiques en famille et entre amis avec suﬃsamment de place pour tout le monde. Quel que soit l’itinéraire que vous empruntiez, entre les villes de Grande-Terre ou de Basse-Terre, notre Renault Captur est le choix parfait pour des promenades sereines.', 'ESSENCE', 800, 'Automatique', '3', '5', '5', 'autoradio, climatisation, fermeture centralisée,', 'CAPTURE NOIR.png', '2024-06-01 19:15:44', 2, 1, NULL, NULL, NULL, NULL, '{\"vendu\":0,\"dateVente\":\"\"}'),
(2, 1, 'DW-277-WE', '2015-10-26', '2024-01-03', 4865, 'Découvrir les villes de la Guadeloupe n’a jamais été aussi simple avec Renault Twingo. C’est aussi une voiture à faible consommation de carburant qui peut vous emmener où vous voulez confortablement.\r\n\r\nCette petite voiture de ﬁtness est proposée dans une gamme de prix pratique et abordable. Le fait de pouvoir facilement la garer n’importe où dans la ville est un énorme bonus.', 'Essence', 800, 'Manuelle', '1', '5', '4', 'autoradio, climatisation, fermeture centralisée', 'a87d84350ce9b16e658b3c3cc935e1e4.png', NULL, 3, 1, 200, 400, NULL, NULL, NULL),
(3, 1, '56-HIR-624AUTO', '2023-12-30', '2024-01-01', 21031, 'Si vous recherchez une voiture qui offre un rapport qualité / prix incroyable, alors la Clio IV, avec sa grande polyvalence, est faite pour vous. Cette voiture de tourisme vous garantit un super confort aussi bien sur les petites rues que sur les grandes routes. Elle a une consommation de carburant raisonnable, et est fiable.', 'Essence', 800, 'Manuelle', '3', '5', '5', 'autoradio, climatisation, fermeture centralisée', '659dcc6378f54773bb75cc43e8d97488.png', NULL, 1, 1, 65596, 65966, '2022-02-05 03:52:59', 4, NULL),
(8, NULL, 'ES-597-BE', '2017-11-16', '2020-02-01', 7000, 'CLIM ECONOMIQUE', 'essence', 700, 'MANUELLE', '2', '5', '4', 'économique', 'TWINGO BLANC.png', '2024-02-18 11:04:01', 3, 1, NULL, NULL, NULL, NULL, NULL),
(9, NULL, 'EJ-676-AG', '2016-12-30', '2020-11-12', 8000, 'ECO', 'essence', 800, 'MANUELLE', '5', '5', '5', 'économique', 'clio 4b.png', '2022-02-02 00:46:52', 1, 1, NULL, NULL, NULL, NULL, '{\"vendu\":\"1\",\"dateVente\":\"\"}'),
(10, NULL, 'EH-873-ZL', '2016-12-29', '2020-12-03', 5000, 'ECO', 'essence', 800, 'MANUELLE', '5', '5', '5', 'économique', 'IMG_20201121_143307.jpg', '2022-02-02 00:51:12', 1, 1, NULL, NULL, NULL, NULL, NULL),
(11, NULL, 'FV-507-PQ', '2020-12-04', '2020-12-04', 10000, 'ECO', 'essence', 800, 'MANUELLE', '5', '5', '5', 'économique', 'CLIO 4a.jpg', '2024-02-18 11:06:18', 1, 1, NULL, NULL, NULL, NULL, NULL),
(12, NULL, 'EN-505-VP', '2017-06-29', '2017-06-29', 7000, 'ECO', 'essence', 800, 'MANUELLE', '5', '5', '5', 'économique', 'clio rouge.png', '2024-02-18 11:03:04', 1, 1, NULL, NULL, NULL, NULL, '{\"vendu\":0,\"dateVente\":\"\"}'),
(13, NULL, 'DY-863-SD', '2016-01-06', '2021-07-13', 10000, 'GPS-CAMERA RADAR DE RECUL CLIM', 'DIESEL', 800, 'AUTO', '5', '5', '5', 'économique', 'CAPTURE NOIR.png', '2024-02-18 11:02:21', 2, 1, 1500, 2000, '2022-02-08 22:42:14', NULL, '{\"vendu\":0,\"dateVente\":\"\"}'),
(14, NULL, 'EF-489-VR', '2016-10-05', '2021-10-05', 5000, 'ECONOMIQUE', 'essence', 800, 'MANUELLE', '5', '5', '5', 'économique', 'CLIO 4a.png', '2022-02-15 12:42:07', 1, 1, NULL, NULL, NULL, NULL, '{\"vendu\":\"1\",\"dateVente\":\"\"}'),
(15, NULL, 'GT-939-FN', '2023-12-28', '2024-01-21', 21031, 'ECONOMIQUE', 'ESSENCE', 800, 'BOITE AUTOMATIQUE', '3', '5', '5', 'ECONOMIQUE', 'clio5.png', '2024-02-18 10:58:50', 10, 1, NULL, NULL, NULL, NULL, NULL),
(16, NULL, 'GT-664-FN', '2023-12-28', '2024-01-01', 21031, 'ECONOMIQUE', 'ESSENCE', 800, 'BOITE AUTOMATIQUE', '3', '5', '5', 'ECONOMIQUE', 'clio5.png', '2024-02-18 10:58:38', 10, 1, NULL, NULL, NULL, NULL, '{\"vendu\":0,\"dateVente\":\"\"}'),
(17, NULL, 'GT-061-FP', '2023-12-28', '2024-01-01', 21031, 'Découvrez le parfait équilibre entre confort, style et performance avec la Renault Clio 5, désormais disponible avec une transmission automatique pour une expérience de conduite encore plus agréable. Que ce soit pour une escapade en ville ou un voyage sur de longues distances, la Clio 5 offre une conduite fluide et une maniabilité exceptionnelle, tout en offrant un design élégant et moderne.', 'ESSENCE', 800, 'BOITE AUTOMATIQUE', '3', '5', '5', 'ECONOMIQUE', 'clio5.png', '2024-02-18 10:58:24', 10, 1, NULL, NULL, NULL, NULL, '{\"vendu\":0,\"dateVente\":\"\"}'),
(18, NULL, 'GT-800-FN', '2023-12-28', '2024-01-01', 21031, 'Découvrez le parfait équilibre entre confort, style et performance avec la Renault Clio 5, désormais disponible avec une transmission automatique pour une expérience de conduite encore plus agréable. Que ce soit pour une escapade en ville ou un voyage sur de longues distances, la Clio 5 offre une conduite fluide et une maniabilité exceptionnelle, tout en offrant un design élégant et moderne.', 'ESSENCE', 800, 'BOITE AUTOMATIQUE', '3', '5', '5', 'Carplay, climatisation, fermeture centralisée, aide à la conduite', 'renault-clio-5-rs-kleber-silva-av.png', '2024-02-22 19:52:38', 10, 1, NULL, NULL, NULL, NULL, NULL);

--
-- Contraintes pour les tables déchargées
--

--
-- Contraintes pour la table `annulation_reservation`
--
ALTER TABLE `annulation_reservation`
  ADD CONSTRAINT `FK_4418C7BBB83297E7` FOREIGN KEY (`reservation_id`) REFERENCES `reservation` (`id`);

--
-- Contraintes pour la table `appel_paiement`
--
ALTER TABLE `appel_paiement`
  ADD CONSTRAINT `FK_CD9A1FBCB83297E7` FOREIGN KEY (`reservation_id`) REFERENCES `reservation` (`id`);

--
-- Contraintes pour la table `avis`
--
ALTER TABLE `avis`
  ADD CONSTRAINT `FK_8F91ABF0B83297E7` FOREIGN KEY (`reservation_id`) REFERENCES `reservation` (`id`);

--
-- Contraintes pour la table `conducteur`
--
ALTER TABLE `conducteur`
  ADD CONSTRAINT `FK_2367714319EB6921` FOREIGN KEY (`client_id`) REFERENCES `user` (`id`);

--
-- Contraintes pour la table `devis`
--
ALTER TABLE `devis`
  ADD CONSTRAINT `FK_8B27C52B19EB6921` FOREIGN KEY (`client_id`) REFERENCES `user` (`id`),
  ADD CONSTRAINT `FK_8B27C52B4A4A3511` FOREIGN KEY (`vehicule_id`) REFERENCES `vehicule` (`id`);

--
-- Contraintes pour la table `devis_garantie`
--
ALTER TABLE `devis_garantie`
  ADD CONSTRAINT `FK_13DC356C41DEFADA` FOREIGN KEY (`devis_id`) REFERENCES `devis` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `FK_13DC356CA4B9602F` FOREIGN KEY (`garantie_id`) REFERENCES `garantie` (`id`) ON DELETE CASCADE;

--
-- Contraintes pour la table `devis_option`
--
ALTER TABLE `devis_option`
  ADD CONSTRAINT `FK_6693C77C41DEFADA` FOREIGN KEY (`devis_id`) REFERENCES `devis` (`id`),
  ADD CONSTRAINT `FK_6693C77CB83297E7` FOREIGN KEY (`reservation_id`) REFERENCES `reservation` (`id`),
  ADD CONSTRAINT `FK_6693C77CCCEFD70A` FOREIGN KEY (`opt_id`) REFERENCES `options` (`id`);

--
-- Contraintes pour la table `devis_options`
--
ALTER TABLE `devis_options`
  ADD CONSTRAINT `FK_42DB61DB3ADB05F1` FOREIGN KEY (`options_id`) REFERENCES `options` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `FK_42DB61DB41DEFADA` FOREIGN KEY (`devis_id`) REFERENCES `devis` (`id`) ON DELETE CASCADE;

--
-- Contraintes pour la table `frais_suppl_resa`
--
ALTER TABLE `frais_suppl_resa`
  ADD CONSTRAINT `FK_9B1D74A2B83297E7` FOREIGN KEY (`reservation_id`) REFERENCES `reservation` (`id`);

--
-- Contraintes pour la table `message`
--
ALTER TABLE `message`
  ADD CONSTRAINT `FK_B6BD307F19EB6921` FOREIGN KEY (`client_id`) REFERENCES `user` (`id`);

--
-- Contraintes pour la table `modele`
--
ALTER TABLE `modele`
  ADD CONSTRAINT `FK_100285584827B9B2` FOREIGN KEY (`marque_id`) REFERENCES `marque` (`id`);

--
-- Contraintes pour la table `paiement`
--
ALTER TABLE `paiement`
  ADD CONSTRAINT `FK_B1DC7A1E19EB6921` FOREIGN KEY (`client_id`) REFERENCES `user` (`id`),
  ADD CONSTRAINT `FK_B1DC7A1E438F5B63` FOREIGN KEY (`mode_paiement_id`) REFERENCES `mode_paiement` (`id`),
  ADD CONSTRAINT `FK_B1DC7A1EB83297E7` FOREIGN KEY (`reservation_id`) REFERENCES `reservation` (`id`);

--
-- Contraintes pour la table `reservation`
--
ALTER TABLE `reservation`
  ADD CONSTRAINT `FK_42C8495514237FB` FOREIGN KEY (`etat_reservation_id`) REFERENCES `etat_reservation` (`id`),
  ADD CONSTRAINT `FK_42C8495519EB6921` FOREIGN KEY (`client_id`) REFERENCES `user` (`id`),
  ADD CONSTRAINT `FK_42C849554A4A3511` FOREIGN KEY (`vehicule_id`) REFERENCES `vehicule` (`id`),
  ADD CONSTRAINT `FK_42C849556776468B` FOREIGN KEY (`mode_reservation_id`) REFERENCES `mode_reservation` (`id`),
  ADD CONSTRAINT `FK_42C84955C1E5BBF9` FOREIGN KEY (`saisisseur_km_id`) REFERENCES `user` (`id`);

--
-- Contraintes pour la table `reservation_conducteur`
--
ALTER TABLE `reservation_conducteur`
  ADD CONSTRAINT `FK_43CDB8F7B83297E7` FOREIGN KEY (`reservation_id`) REFERENCES `reservation` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `FK_43CDB8F7F16F4AC6` FOREIGN KEY (`conducteur_id`) REFERENCES `conducteur` (`id`) ON DELETE CASCADE;

--
-- Contraintes pour la table `reservation_garantie`
--
ALTER TABLE `reservation_garantie`
  ADD CONSTRAINT `FK_EC26243CA4B9602F` FOREIGN KEY (`garantie_id`) REFERENCES `garantie` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `FK_EC26243CB83297E7` FOREIGN KEY (`reservation_id`) REFERENCES `reservation` (`id`) ON DELETE CASCADE;

--
-- Contraintes pour la table `reservation_options`
--
ALTER TABLE `reservation_options`
  ADD CONSTRAINT `FK_B7A041023ADB05F1` FOREIGN KEY (`options_id`) REFERENCES `options` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `FK_B7A04102B83297E7` FOREIGN KEY (`reservation_id`) REFERENCES `reservation` (`id`) ON DELETE CASCADE;

--
-- Contraintes pour la table `reset_password`
--
ALTER TABLE `reset_password`
  ADD CONSTRAINT `FK_B9983CE5A76ED395` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`);

--
-- Contraintes pour la table `tarifs`
--
ALTER TABLE `tarifs`
  ADD CONSTRAINT `FK_F9B8C4964827B9B2` FOREIGN KEY (`marque_id`) REFERENCES `marque` (`id`),
  ADD CONSTRAINT `FK_F9B8C496AC14B70A` FOREIGN KEY (`modele_id`) REFERENCES `modele` (`id`);

--
-- Contraintes pour la table `user`
--
ALTER TABLE `user`
  ADD CONSTRAINT `FK_8D93D6491DF27D6E` FOREIGN KEY (`infos_resa_id`) REFERENCES `infos_resa` (`id`),
  ADD CONSTRAINT `FK_8D93D649B62F3B9C` FOREIGN KEY (`infos_vol_resa_id`) REFERENCES `infos_vol_resa` (`id`);

--
-- Contraintes pour la table `vehicule`
--
ALTER TABLE `vehicule`
  ADD CONSTRAINT `FK_292FFF1D4827B9B2` FOREIGN KEY (`marque_id`) REFERENCES `marque` (`id`),
  ADD CONSTRAINT `FK_292FFF1DAC14B70A` FOREIGN KEY (`modele_id`) REFERENCES `modele` (`id`),
  ADD CONSTRAINT `FK_292FFF1DC1E5BBF9` FOREIGN KEY (`saisisseur_km_id`) REFERENCES `user` (`id`),
  ADD CONSTRAINT `FK_292FFF1DC54C8C93` FOREIGN KEY (`type_id`) REFERENCES `type` (`id`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
