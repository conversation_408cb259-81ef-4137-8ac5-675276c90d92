# Use the official PHP image, specifying the version (7.4) and Apache
FROM php:7.4-apache

# Enable Apache mod_rewrite (important for Symfony routing)
RUN a2enmod rewrite

# Install PHP extensions required by Symfony
RUN apt-get update && apt-get install -y \
    git \
    zip \
    unzip \
    libicu-dev \
    libonig-dev \
    libzip-dev \
    && docker-php-ext-install \
    pdo \
    pdo_mysql \
    intl \
    zip
# Install PHP extensions required by Symfony
RUN apt-get update && apt-get install -y \
    git \
    zip \
    unzip \
    curl \
    libicu-dev \
    libonig-dev \
    libzip-dev \
    libpng-dev \
    libjpeg-dev \
    libfreetype6-dev \
    && docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install \
    pdo \
    pdo_mysql \
    intl \
    zip \
    gd


# 👉 Install Node.js + npm (using NodeSource for latest LTS)
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash - \
    && apt-get install -y nodejs

# Verify installation
RUN node -v && npm -v

# Install the EXACT same Composer version as Windows (2.8.2)
RUN curl -sS https://getcomposer.org/installer | php -- --version=2.8.2 --install-dir=/usr/local/bin --filename=composer

# Add user management to fix ownership issues
ARG USER_ID=1000
ARG GROUP_ID=1000
RUN groupadd -g ${GROUP_ID} appuser || true
RUN useradd -u ${USER_ID} -g ${GROUP_ID} -m appuser || true

# Set working directory
WORKDIR /var/www/html

# Change ownership and switch user
RUN chown -R appuser:appuser /var/www/html
USER appuser

# Expose port 80 (default for Apache)
EXPOSE 80
